import Testing
import SwiftUI
import UniformTypeIdentifiers
import Foundation
@testable import CCAIChatSwiftUI

/// Unit tests for `TranscriptDocument` struct.
///
/// Tests cover document initialization, file operations, and filename generation functionality.
struct TranscriptDocumentTest {
    // MARK: - Test Data

    /// Sample PDF data for testing
    private let samplePDFData = Data("Sample PDF content".utf8)

    /// Sample company name for testing
    private let sampleCompanyName = "ACME Corporation"

    // MARK: - Initialization Tests

    @Test func initializationWithDataAndCompanyName() {
        let document = TranscriptDocument(data: samplePDFData, companyDisplayName: sampleCompanyName)

        #expect(document.data == samplePDFData)
        #expect(document.companyDisplayName == sampleCompanyName)
    }

    // MARK: - Readable Content Types Tests

    @Test func readableContentTypes() {
        let contentTypes = TranscriptDocument.readableContentTypes

        #expect(contentTypes.count == 1)
        #expect(contentTypes.contains(.pdf))
    }

    // MARK: - Filename Generation Tests

    @Test func staticFileNameGenerationWithCompanyName() {
        let fileName = TranscriptDocument.getFileName(companyDisplayName: sampleCompanyName)

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript with \(sampleCompanyName) on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }

    @Test func staticFileNameGenerationWithoutCompanyName() {
        let fileName = TranscriptDocument.getFileName(companyDisplayName: nil)

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }

    @Test func staticFileNameGenerationWithEmptyCompanyName() {
        let fileName = TranscriptDocument.getFileName(companyDisplayName: "")

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }

    @Test func instanceFileNameGenerationWithCompanyName() {
        let document = TranscriptDocument(data: samplePDFData, companyDisplayName: sampleCompanyName)
        let fileName = document.getFileName()

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript with \(sampleCompanyName) on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }

    @Test func instanceFileNameGenerationWithoutCompanyName() {
        let document = TranscriptDocument(data: samplePDFData)
        let fileName = document.getFileName()

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }

    @Test func instanceFileNameGenerationWithEmptyCompanyName() {
        let document = TranscriptDocument(data: samplePDFData, companyDisplayName: "")
        let fileName = document.getFileName()

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let expectedDate = dateFormatter.string(from: Date())
        let expectedFileName = "Chat Transcript on \(expectedDate)"

        #expect(fileName == expectedFileName)
    }
}
