import Testing
import Combine
import Foundation
@testable import CCAIChatSwiftUI
@testable import CCAIChat
@testable import CCAIKit

// swiftlint:disable file_length
extension ChatViewModelTest {
    @Test func downloadTranscriptFileNameFormat() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let fileURL = try #require(await viewModel.downloadTranscript(fileName: testName))

        // Verify filename format
        let fileName = fileURL.lastPathComponent
        #expect(fileName == "\(testName).pdf")

        // Clean up
        try? FileManager.default.removeItem(at: fileURL)
    }

    @Test func downloadChatTranscriptError() async throws {
        await startChat()
        stubCompanySettings()

        let transcriptId = 200

        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        networkService.stub(
            .init(method: .GET,
                  path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: """
            {
                "status": "in_progress"
            }
            """
        )

        let result = await viewModel.downloadTranscript()
        #expect(result == nil)
    }

    @Test func downloadTranscriptSuccess() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let fileURL = try #require(await viewModel.downloadTranscript(fileName: testName))

        // Verify result
        let fileData = try Data(contentsOf: fileURL)
        #expect(fileData == pdfContent.data(using: .utf8))

        // Verify filename format
        let fileName = fileURL.lastPathComponent
        #expect(fileName == "\(testName).pdf")

        // Clean up
        try? FileManager.default.removeItem(at: fileURL)
    }

    @Test func downloadTranscriptWithoutCompanyResponse() async throws {
        await startChat()

        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let fileURL = try #require(await viewModel.downloadTranscript(fileName: testName))

        let fileName = fileURL.lastPathComponent
        #expect(fileName == "\(testName).pdf")

        try? FileManager.default.removeItem(at: fileURL)
    }

    @Test func downloadTranscriptWithNullTranscriptId() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript to return null transcript ID
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": null
            }
            """
        )

        // Test download transcript
        let result = await viewModel.downloadTranscript()

        // Verify result is nil when transcript ID is null
        #expect(result == nil)
    }

    @Test func downloadTranscriptWithEmptyDisplayName() async throws {
        await startChat()

        // Override company settings with empty display name
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.company),
            withJSON: """
            {
                "display_name": "",
                "phone_number_threshold": 0.5
            }
            """
        )

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let fileURL = try #require(await viewModel.downloadTranscript(fileName: testName))

        // Verify result
        let fileName = fileURL.lastPathComponent
        #expect(fileName == "\(testName).pdf")

        // Clean up
        try? FileManager.default.removeItem(at: fileURL)
    }

    @Test func downloadTranscriptWithNilDisplayName() async throws {
        await startChat()

        // Override company settings with nil display name
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.company),
            withJSON: """
            {
                "phone_number_threshold": 0.5
            }
            """
        )

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let fileURL = try #require(await viewModel.downloadTranscript(fileName: testName))

        // Verify result
        let fileName = fileURL.lastPathComponent
        #expect(fileName == "\(testName).pdf")

        // Clean up
        try? FileManager.default.removeItem(at: fileURL)
    }

    @Test func downloadTranscriptRunningActionState() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        let testName = try #require(Test.current?.name)

        // Test download transcript
        let task = Task {
            await viewModel.downloadTranscript(fileName: testName)
        }

        // Wait for completion
        _ = await task.value

        // Verify running action is cleared after download
        #expect(viewModel.runningAction == nil)

        // Clean up
        if let result = await viewModel.downloadTranscript(fileName: testName) {
            try? FileManager.default.removeItem(at: result)
        }
    }

    @Test func downloadTranscriptDataSuccess() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript data response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        // Test download transcript data
        let data = try #require(await viewModel.downloadTranscriptData())

        #expect(data == pdfContent.data(using: .utf8))
    }

    @Test func downloadTranscriptDataError() async throws {
        await startChat()
        stubCompanySettings()

        let transcriptId = 200

        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        networkService.stub(
            .init(method: .GET,
                  path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: """
            {
                "status": "in_progress"
            }
            """
        )

        let result = await viewModel.downloadTranscriptData()
        #expect(result == nil)
    }

    @Test func downloadTranscriptDataWithoutCompanyResponse() async throws {
        await startChat()

        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        // Test download transcript data without company response
        let result = await viewModel.downloadTranscriptData()

        // Verify result is not nil even company is not fetched
        #expect(result != nil)
    }

    @Test func downloadTranscriptDataRunningActionState() async throws {
        await startChat()
        stubCompanySettings()

        // Mock generate transcript response
        let transcriptId = 200
        networkService.stub(
            .init(method: .POST, path: APIEndpoint.generateChatTranscript(chatId: chatId)),
            withJSON: """
            {
                "chat_transcript_id": \(transcriptId)
            }
            """
        )

        // Mock download transcript data response
        let pdfContent = "PDF content"
        networkService.stub(
            .init(method: .GET, path: APIEndpoint.downloadChatTranscriptFile(transcriptId: transcriptId)),
            withJSON: pdfContent,
            headers: ["Content-Type": "application/pdf"]
        )

        // Verify running action is set during download
        let task = Task {
            await viewModel.downloadTranscriptData()
        }

        // Wait for completion
        _ = await task.value

        // Verify running action is cleared after download
        #expect(viewModel.runningAction == nil)
    }

    @Test func downloadTranscriptRunningActionClearedOnError() async throws {
        await startChat()

        // No company settings stubbed, so it will fail

        // Test download transcript with error
        let result = await viewModel.downloadTranscript()

        // Verify result is nil and running action is cleared
        #expect(result == nil)
        #expect(viewModel.runningAction == nil)
    }

    @Test func downloadTranscriptDataRunningActionClearedOnError() async throws {
        await startChat()

        // No company settings stubbed, so it will fail

        // Test download transcript data with error
        let result = await viewModel.downloadTranscriptData()

        // Verify result is nil and running action is cleared
        #expect(result == nil)
        #expect(viewModel.runningAction == nil)
    }

    @Test func prepareForTranscriptDownloadWithCachedCompanyResponse() async throws {
        await startChat()
        stubCompanySettings()

        // First call should fetch company response
        _ = await viewModel.downloadTranscript()

        // Reset running action for second test
        viewModel.runningAction = nil

        // Second call should use cached response
        let result = await viewModel.downloadTranscriptData()

        // Should still work with cached company response
        #expect(result != nil || viewModel.errorMessage != nil) // Either success or another error, but not company fetch error
    }

    @Test func updateAtLeastOneMessageExchangedFlag() {
        // Test with no messages
        viewModel.messages = []
        viewModel.atLeastOneMessageExchanged = false
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == false)

        // Test with only incoming messages
        let incomingMessage = Message(
            senderId: "agent-1",
            timestamp: Date(),
            displayContent: .text(text: "Hello")
        )
        viewModel.messages = [incomingMessage]
        viewModel.atLeastOneMessageExchanged = false
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == false)

        // Test with only outgoing message
        let outgoingMessage = Message(
            senderId: "end_user-1",
            timestamp: Date(),
            displayContent: .text(text: "Hi")
        )
        viewModel.messages = [outgoingMessage]
        viewModel.atLeastOneMessageExchanged = false
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == false)

        // Test with outgoing and one incoming message
        viewModel.messages = [outgoingMessage, incomingMessage]
        viewModel.atLeastOneMessageExchanged = false
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == false)

        // Test with outgoing and two incoming messages (should be true)
        let incomingMessage2 = Message(
            senderId: "agent-2",
            timestamp: Date(),
            displayContent: .text(text: "How can I help?")
        )
        viewModel.messages = [outgoingMessage, incomingMessage, incomingMessage2]
        viewModel.atLeastOneMessageExchanged = false
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == true)

        // Test that flag doesn't change once set to true
        viewModel.messages = [incomingMessage]
        viewModel.atLeastOneMessageExchanged = true
        viewModel.updateAtLeastOneMessageExchangedFlag()
        #expect(viewModel.atLeastOneMessageExchanged == true)
    }

    @Test func doesMessageExchanged() {
        // Test with no messages
        viewModel.messages = []
        #expect(viewModel.doesMessageExchanged() == false)

        // Test with only incoming messages
        let incomingMessage = Message(
            senderId: "agent-1",
            timestamp: Date(),
            displayContent: .text(text: "Hello")
        )
        viewModel.messages = [incomingMessage]
        #expect(viewModel.doesMessageExchanged() == false)

        // Test with only outgoing message
        let outgoingMessage = Message(
            senderId: "end_user-1",
            timestamp: Date(),
            displayContent: .text(text: "Hi")
        )
        viewModel.messages = [outgoingMessage]
        #expect(viewModel.doesMessageExchanged() == false)

        // Test with outgoing and one incoming message
        viewModel.messages = [outgoingMessage, incomingMessage]
        #expect(viewModel.doesMessageExchanged() == false)

        // Test with outgoing and two incoming messages (should be true)
        let incomingMessage2 = Message(
            senderId: "agent-2",
            timestamp: Date(),
            displayContent: .text(text: "How can I help?")
        )
        viewModel.messages = [outgoingMessage, incomingMessage, incomingMessage2]
        #expect(viewModel.doesMessageExchanged() == true)

        // Test with multiple outgoing and multiple incoming messages
        let outgoingMessage2 = Message(
            senderId: "end_user-1",
            timestamp: Date(),
            displayContent: .text(text: "Thanks")
        )
        viewModel.messages = [outgoingMessage, incomingMessage, outgoingMessage2, incomingMessage2]
        #expect(viewModel.doesMessageExchanged() == true)

        // Test with mixed order
        viewModel.messages = [incomingMessage, outgoingMessage, incomingMessage2]
        #expect(viewModel.doesMessageExchanged() == true)
    }

    @Test func doesMessageExchangedWithHistoryFilter() {
        let historyTimestamp = Date(timeIntervalSince1970: 1000)
        let currentTimestamp = Date(timeIntervalSince1970: 2000)

        // Set max history message timestamp
        viewModel.maxHistoryMessageTimestamp = historyTimestamp

        // Create history messages (before threshold)
        let historyOutgoing = Message(
            senderId: "end_user-1",
            timestamp: Date(timeIntervalSince1970: 500),
            displayContent: .text(text: "History outgoing")
        )
        let historyIncoming1 = Message(
            senderId: "agent-1",
            timestamp: Date(timeIntervalSince1970: 600),
            displayContent: .text(text: "History incoming 1")
        )
        let historyIncoming2 = Message(
            senderId: "agent-1",
            timestamp: Date(timeIntervalSince1970: 700),
            displayContent: .text(text: "History incoming 2")
        )

        // Create current session messages (after threshold)
        let currentOutgoing = Message(
            senderId: "end_user-1",
            timestamp: currentTimestamp,
            displayContent: .text(text: "Current outgoing")
        )
        let currentIncoming1 = Message(
            senderId: "agent-1",
            timestamp: Date(timeIntervalSince1970: 2100),
            displayContent: .text(text: "Current incoming 1")
        )
        let currentIncoming2 = Message(
            senderId: "agent-1",
            timestamp: Date(timeIntervalSince1970: 2200),
            displayContent: .text(text: "Current incoming 2")
        )

        // Test with history messages that would qualify but should be filtered out
        viewModel.messages = [historyOutgoing, historyIncoming1, historyIncoming2]
        #expect(viewModel.doesMessageExchanged() == false, "Should not count history messages")

        // Test with current session messages only
        viewModel.messages = [currentOutgoing, currentIncoming1, currentIncoming2]
        #expect(viewModel.doesMessageExchanged() == true, "Should count current session messages")

        // Test with mix of history and current messages
        viewModel.messages = [historyOutgoing, historyIncoming1, historyIncoming2, currentOutgoing, currentIncoming1, currentIncoming2]
        #expect(viewModel.doesMessageExchanged() == true, "Should only count current session messages in mixed scenario")

        // Test with only history messages that would qualify
        viewModel.messages = [historyOutgoing, historyIncoming1, historyIncoming2]
        #expect(viewModel.doesMessageExchanged() == false, "Should not count only history messages")

        // Test with insufficient current session messages
        viewModel.messages = [historyOutgoing, historyIncoming1, historyIncoming2, currentOutgoing, currentIncoming1]
        #expect(viewModel.doesMessageExchanged() == false, "Should require 2+ current incoming messages")
    }

    @Test func doesMessageExchangedWithoutHistoryFilter() {
        // Ensure no history filter is set
        viewModel.maxHistoryMessageTimestamp = nil

        let outgoingMessage = Message(
            senderId: "end_user-1",
            timestamp: Date(),
            displayContent: .text(text: "Hi")
        )
        let incomingMessage1 = Message(
            senderId: "agent-1",
            timestamp: Date(),
            displayContent: .text(text: "Hello")
        )
        let incomingMessage2 = Message(
            senderId: "agent-2",
            timestamp: Date(),
            displayContent: .text(text: "How can I help?")
        )

        // Test with qualifying messages and no filter
        viewModel.messages = [outgoingMessage, incomingMessage1, incomingMessage2]
        #expect(viewModel.doesMessageExchanged() == true, "Should count all messages when no history filter")
    }
}
// swiftlint:enable file_length
