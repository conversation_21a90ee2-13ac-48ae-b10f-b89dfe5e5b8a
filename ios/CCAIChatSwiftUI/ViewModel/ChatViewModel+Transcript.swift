import Foundation
import CCAIKit
import CCAIChat

extension ChatViewModel {
    /// Prepares for transcript download by setting running action and ensuring company response is available.
    /// - Returns: `true` if preparation was successful, `false` otherwise.
    private func prepareForTranscriptDownload() async {
        runningAction = .downloadTranscript

        if companyResponse == nil {
            do {
                companyResponse = try await companyService?.get()
            } catch {
                LoggingUtil.shared.log("Failed to fetch company: \(error)", level: .error)
            }
        }
    }

    /// Downloads the chat transcript as a PDF file.
    /// - Parameter fileName: Optional custom filename for the transcript PDF. If `nil`, a default filename will be generated using the company display name.
    /// - Returns: URL of the downloaded transcript file, or `nil` if download failed.
    func downloadTranscript(fileName: String? = nil) async -> URL? {
        defer {
            runningAction = nil
        }

        await prepareForTranscriptDownload()

        do {
            let savePath = NSTemporaryDirectory()
            let fileName = fileName ?? TranscriptDocument.getFileName(companyDisplayName: companyResponse?.displayName)
            let fileURL = URL(fileURLWithPath: savePath).appendingPathComponent("\(fileName).pdf")
            try await service?.downloadChatTranscript(to: fileURL)

            return fileURL
        } catch {
            handleError(error, message: "Failed to download transcript.")
        }
        return nil
    }

    /// Downloads the chat transcript as raw data.
    /// - Returns: Data of the transcript, or `nil` if download failed.
    func downloadTranscriptData() async -> Data? {
        defer {
            runningAction = nil
        }

        await prepareForTranscriptDownload()

        do {
            return try await service?.downloadChatTranscriptData()
        } catch {
            handleError(error, message: "Failed to download transcript.")
        }
        return nil
    }

    func updateAtLeastOneMessageExchangedFlag() {
        #warning("Should check !isPostSessionVaShowing as well in the guard")
        guard !atLeastOneMessageExchanged else {
            return
        }
        atLeastOneMessageExchanged = doesMessageExchanged()
    }

    func doesMessageExchanged() -> Bool {
        var incomingCount = 0
        var outgoingFound = false
        var currentSessionMessages = messages
        if let maxHistoryMessageTimestamp = maxHistoryMessageTimestamp {
            currentSessionMessages = messages.filter { $0.timestamp > maxHistoryMessageTimestamp }
        }

        for message in currentSessionMessages {
            if message.isSender {
                outgoingFound = true
            } else {
                incomingCount += 1
            }
        }

        if outgoingFound && incomingCount >= 2 {
            return true
        }
        return false
    }
}
