import SwiftUI
import UniformTypeIdentifiers
import Foundation

/// A document that represents a chat transcript PDF file for export.
///
/// This document conforms to `FileDocument` to enable seamless integration with SwiftUI's
/// `.fileExporter` modifier, allowing users to save chat transcripts to their preferred location.
///
/// ## Usage
/// ```swift
/// let transcriptDocument = TranscriptDocument(data: pdfData, companyDisplayName: "ACME Corp")
/// // Use with .fileExporter modifier
/// ```
///
/// - Note: This document specifically handles PDF data as chat transcripts are generated in PDF format.
struct TranscriptDocument: FileDocument {
    /// The content types that this document can read and write.
    static var readableContentTypes: [UTType] { [.pdf] }

    /// The PDF data of the chat transcript.
    var data: Data

    /// The display name of the company for filename generation.
    var companyDisplayName: String?

    /// Creates a new transcript document with the given PDF data.
    /// - Parameters:
    ///   - data: The PDF data representing the chat transcript content to be exported. This should contain the complete PDF document data.
    ///   - companyDisplayName: An optional company display name used for generating descriptive filenames. When provided, the filename will include "with [companyName]". When nil or empty, "with" is omitted from the filename. Defaults to nil.
    init(data: Data, companyDisplayName: String? = nil) {
        self.data = data
        self.companyDisplayName = companyDisplayName
    }

    /// Creates a transcript document from a file configuration.
    /// - Parameter configuration: The read configuration containing the file data.
    /// - Throws: `CocoaError.fileReadCorruptFile` if the file data cannot be read.
    init(configuration: ReadConfiguration) throws {
        guard let fileData = configuration.file.regularFileContents else {
            throw CocoaError(.fileReadCorruptFile)
        }
        data = fileData
        companyDisplayName = nil
    }

    /// Creates a file wrapper for writing the transcript document.
    /// - Parameter configuration: The write configuration.
    /// - Returns: A file wrapper containing the PDF data.
    /// - Throws: An error if the file wrapper cannot be created.
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        return FileWrapper(regularFileWithContents: data)
    }

    /// Generates a filename for the chat transcript.
    /// - Parameter companyDisplayName: The company display name to include in the filename.
    /// - Returns: A formatted filename including company name and timestamp.
    static func getFileName(companyDisplayName: String? = nil) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let date = dateFormatter.string(from: Date())

        if let companyName = companyDisplayName, !companyName.isEmpty {
            return "Chat Transcript with \(companyName) on \(date)"
        } else {
            return "Chat Transcript on \(date)"
        }
    }

    /// Generates a filename for the chat transcript using the document's company name.
    /// - Returns: A formatted filename including company name and timestamp.
    func getFileName() -> String {
        return TranscriptDocument.getFileName(companyDisplayName: companyDisplayName)
    }
}
