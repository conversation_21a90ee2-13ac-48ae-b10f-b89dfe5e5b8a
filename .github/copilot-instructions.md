# Copilot Instruction

## Documentations

* Use DocC syntax for Swift and KDoc for Kotlin.
* A comprehensive file-level documentation comment.
* Documentation for all public properties and methods.
* Examples where appropriate.
* Parameter descriptions for public methods.
* Clear descriptions of functionality and purpose.

## Code Styles

* Lines should not have trailing whitespace including empty lines.
* Colons should be next to the identifier when specifying a type and next to the key in dictionary literals.
* For Swift, Prefer non-optional `Data(_:)` initializer when converting String to Data. For example,

```swift
let data = Data("{}".utf8)
let response = try JSONDecoder.decode(MyStruct.self, from: data)
```

## Writing Swift Unit Tests

1. File Setup

* Create a test file if it doesn't exist with the same folder structure.
* Import the relevant framework in the newly created test file. For example, if the source code is located in the `CCAIKit` folder, the testing file should import the module.

```swift
import Testing
@testable import CCAIKit
```

1. Test Suite Structure

* Define test suites using a struct.
* Use `@Test` attributes for test functions.

Example:

```swift
struct MyFeatureTest {
    @Test func exampleAssertion() {
        #expect(1 + 1 == 2)
    }
}
```

1. Writing Test Cases

* Use `#expect` for assertions.
* Keep test names descriptive and concise.
* Prefer testing single responsibilities per test.
* The function name should not start with `test`.

Example:

```swift
@Test func userInitialization() {
    let user = User(name: "Alice", age: 30)
    #expect(user.name == "Alice")
    #expect(user.age == 30)
}
```

* When writing a unit test for a model class, check whether it conforms to `Decoder` or `Encoder`. For example, if it conforms to `Decoder`, the unit test should include JSON decoding. Ensure that JSON keys are in snake_case and are decoded into camelCase using `JSONDecoder`.

1. Setup and Teardown

* Use initializers and deinitializers instead of `setUp()` and `tearDown()`.

Example:

```swift
class DatabaseTests {
    let db: Database
    
    init() {
        db = Database()
    }
    
    deinit {
        db.close()
    }
}
```

* Convert to class from struct if it uses `deinit`.

1. Handling Async Tests

* Use `@Test` with `async` when testing asynchronous code.

Example:

```swift
@Test func asyncFunction() async {
    let result = await fetchData()
    #expect(result == "Success")
}
```

1. Mocking & Dependency Injection

* Use mock objects or dependency injection to isolate tests.
* Avoid using real network requests or databases in unit tests.

Example:

```swift
struct MockService: Service {
    func fetchData() -> String {
        return "Mock Data"
    }
}
```

1. Test Organization

* Group related tests in logical structures.
* Name test files with the `Test` suffix. For example, `FeatureTest.swift`

Example:

```
CCAIKit/
  ├── Model
  │   ├── User.swift
CCAIKitTests/
  ├── Model
  │   ├── UserTest.swift
```

1. Best Practices

* Keep tests small and focused.
* Use meaningful failure messages.
* Avoid test dependencies between cases.
* Try to maximize the code coverage.

1. Further reference

* https://developer.apple.com/documentation/testing/migratingfromxctest Refer to this material to see the difference between XCTest.
