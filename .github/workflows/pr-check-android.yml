name: Android PR Check

on:
  pull_request:
    paths:
      - 'android/**'

jobs:
  android-lint:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4
      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android
      - run: bundle exec fastlane android lint
      - uses: yutailang0119/action-android-lint@v5
        with:
          report-path: '**/build/reports/*.xml'
        continue-on-error: false

  run-test:
    runs-on: macos-15-xlarge
    timeout-minutes: 30
    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4
      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: gradle-${{ runner.os }}-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            gradle-${{ runner.os }}-

      - name: run tests
        run: ./android/gradlew testReleaseUnitTest jacocoTestReport --max-workers=2 -p android

      - name: Install xmllint
        run: brew install libxml2

      - name: 'Make coverage script executable'
        run: chmod +x .github/scripts/generate_coverage_report_android.sh

      - name: 'Generate code coverage report'
        working-directory: android
        run: |
          # Fetch the base branch and generate coverage report
          git fetch origin ${{ github.base_ref }}
          ../.github/scripts/generate_coverage_report_android.sh ${{ github.base_ref }}
          
          # Output the filtered coverage report
          cat coverage_report.txt

      - name: 'Post code coverage'
        uses: thollander/actions-comment-pull-request@v3
        with:
          file-path: android/coverage_report.txt

      # Upload test results in case of errors
      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: gradle-test-artifact
          path: android/TestApp/build/reports/tests
