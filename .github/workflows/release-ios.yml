name: Release iOS
on:
  workflow_dispatch:
    inputs:
      target:
        description: 'Target'
        required: true
        default: 'qa'
        type: choice
        options:
        - qa
        - rc
        - production
      qa_target_version:
        description: 'Current PI number for qa target and main branch'
        required: false
        type: string
      rc_version:
        description: 'Optional version for rc'
        required: false
        type: string
      skip_release_app:
        description: 'Skip release app step'
        required: false
        type: boolean
        default: false
      skip_production_deploy:
        description: 'Skip production deploy step'
        required: false
        type: boolean
        default: false
      release_notes:
        description: 'Release Notes'
        required: false
        type: string
jobs:
  # Test job that runs first for all targets
  test:
    runs-on: macos-15-xlarge
    timeout-minutes: 20

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Run test'
        run: bundle exec fastlane scan

  # Main release job that runs after tests pass
  release-app:
    if: ${{ github.event.inputs.skip_release_app == 'false' }}
    needs: test
    runs-on: macos-15-xlarge
    timeout-minutes: 20

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Release test app'
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
          FIREBASE_GROUPS: ${{ vars.FIREBASE_GROUPS_IOS }}
          MATCH_GIT_PRIVATE_KEY: ${{ secrets.MATCH_GIT_PRIVATE_KEY }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
        run: |
          firebase_app_id=$(echo '${{ vars.FIREBASE_APP_ID_IOS }}' | jq -r '.${{ github.event.inputs.target }}')

          bundle exec fastlane ios release \
            branch:${{ github.ref_name }} \
            target:${{ github.event.inputs.target }} \
            qa_target_version:${{ github.event.inputs.qa_target_version }} \
            release_notes:"${{ github.event.inputs.release_notes }}" \
            firebase_app_id:"${firebase_app_id}"

      - name: Upload App to LambdaTest for E2E testings
        env:
          LT_USER: ${{ secrets.LT_USER }}
          LT_API_KEY: ${{ secrets.LT_API_KEY }}
        run: |
          bundle exec fastlane run upload_app \
            platform:ios \
            target:${{ github.event.inputs.target }}

  # RC job that runs in parallel with release job after tests pass
  rc-build-and-upload:
    if: ${{ github.event.inputs.target == 'rc' }}
    needs: test
    runs-on: macos-15-xlarge
    timeout-minutes: 20

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Update versions'
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          firebase_app_id=$(echo '${{ vars.FIREBASE_APP_ID_IOS }}' | jq -r '.rc')

          bundle exec fastlane run update_version_and_build_number \
            platform:ios \
            target:rc \
            branch:${{ github.ref_name }} \
            firebase_app_id:"${firebase_app_id}"

      - name: 'Build frameworks'
        run: |
          bundle exec fastlane run build_ios_frameworks

      - name: 'Configure AWS Credentials'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 'Upload SDKs to staging folder'
        env:
          AWS_S3_URI: ${{ vars.AWS_S3_URI }}
        run: |
          bundle exec fastlane run upload_sdk \
            platform:ios \
            target:rc \
            rc_version:${{ github.event.inputs.rc_version }}

  # Production job that runs in parallel with release job after tests pass
  production-deploy:
    if: ${{ github.event.inputs.target == 'production' && github.event.inputs.skip_production_deploy == 'false' }}
    runs-on: macos-15-xlarge
    timeout-minutes: 20

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Install mint'
        run: brew install mint

      - name: 'Cache mint packages'
        uses: actions/cache@v3
        with:
          path: ~/.mint
          key: mint-${{ hashFiles('Mintfile') }}

      - name: 'Install mint packages'
        run: 'mint bootstrap'

      - name: 'Configure AWS Credentials'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 'Copy SDKs from staging to production folder'
        env:
          AWS_S3_URI: ${{ vars.AWS_S3_URI }}
        run: |
          bundle exec fastlane run copy_sdk platform:ios

      - name: 'Create GitHub App token'
        id: app-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.SWIFT_PACKAGE_GITHUB_APP_ID }}
          private-key: ${{ secrets.SWIFT_PACKAGE_GITHUB_APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}
          repositories: |
            ujet-mobile-sdk-v3
            ujet-ios-sdk-sp

      - name: 'Deploy Swift Package'
        run: |
          git config --global credential.helper store
          echo "https://x-access-token:${{ steps.app-token.outputs.token }}@github.com" > ~/.git-credentials
          bundle exec fastlane run push_swift_package \
            s3_uri:${{ vars.AWS_S3_URI }}
