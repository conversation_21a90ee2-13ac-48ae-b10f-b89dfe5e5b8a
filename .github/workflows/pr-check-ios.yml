name: iOS PR Check

on:
  pull_request:
    paths:
      - 'ios/**'

jobs:
  swiftlint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: cirruslabs/swiftlint-action@v1
        with:
          args: --working-directory ios

  run-unit-test:
    runs-on: macos-15-xlarge
    timeout-minutes: 30
    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Run test'
        run: bundle exec fastlane scan

      - name: 'Code coverage'
        id: slather
        run: |
          # Fetch the base branch and generate coverage report
          git fetch origin ${{ github.base_ref }}
          .github/scripts/generate_coverage_report_ios.sh ${{ github.base_ref }}

          cat coverage_report.txt

      - name: 'Post code coverage'
        uses: thollander/actions-comment-pull-request@v3
        with:
          file-path: coverage_report.txt

      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: xcodebuild-artifact
          path: build/DerivedData/Logs/Test

  run-e2e-test:
    runs-on: macos-15-xlarge
    if: false
    timeout-minutes: 30
    strategy:
      matrix:
        device_name: ['iPhone 16 Pro']
        os_version: ['18.5']
    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup iOS environment'
        uses: ./.github/actions/setup-ios

      - name: 'Checkout e2e repo'
        uses: actions/checkout@v4
        with:
          repository: UJET/ujet-qa-e2e
          ref: proj-headless-sdk-phase-1/MBL-1075-ios-v3-e2e-local
          path: ujet-qa-e2e
          token: ${{ secrets.PA_TOKEN }}

      - name: Cache Node.js modules
        id: cache-npm
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('ujet-qa-e2e/**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: 'ujet-qa-e2e/.nvmrc'

      - name: Install dependencies
        run: npm ci
        working-directory: ujet-qa-e2e

      - name: 'Build iOS app for testing'
        run: |
          SCAN_BUILD_FOR_TESTING=true bundle exec fastlane scan

      - name: 'Find and boot iOS Simulator'
        run: |
          xcrun simctl list devices
          
          result=$(bundle exec fastlane ios get_simulator_device_id device_name:"${{ matrix.device_name }}" os_version:"${{ matrix.os_version }}" | grep 'Device ID:' | sed 's/.*Device ID: //')
          echo "Found simulator: $result"
          echo "SIMULATOR_DEVICE_ID=$result" >> $GITHUB_ENV
          # Boot the simulator using the local variable
          xcrun simctl boot "$result" || true
          # Check the boot status
          xcrun simctl bootstatus "$result" -b

      - name: 'Install app on simulator'
        run: |
          # Find the built app and install it
          echo "Looking for built apps..."
          find . -name "*.app" -path "*/Build/Products/*"

          APP_PATH=$(find . -name "*.app" -path "*/Build/Products/*" | grep -v "UITests" | grep -v "Tests-Runner" | head -1)
          if [ -n "$APP_PATH" ]; then
            echo "Installing app: $APP_PATH"
            xcrun simctl install "$SIMULATOR_DEVICE_ID" "$APP_PATH"

            # Verify installation and get bundle ID
            echo "Verifying app installation..."
            BUNDLE_ID=$(xcrun simctl listapps "$SIMULATOR_DEVICE_ID" | grep -A 5 -B 5 "$(basename "$APP_PATH" .app)" | grep "CFBundleIdentifier" | head -1 | sed 's/.*= "\(.*\)";/\1/')
            echo "Installed app bundle ID: $BUNDLE_ID"

            # List all installed apps for debugging
            echo "All installed apps on simulator:"
            xcrun simctl listapps "$SIMULATOR_DEVICE_ID"
          else
            echo "No .app file found in build products"
            echo "Available files in build directory:"
            find . -path "*/Build/Products/*" -name "*.app" -o -name "*.ipa"
            exit 1
          fi

      - name: 'Start Appium server'
        run: |
          echo "Starting Appium server..."
          npx appium --log-level info --port 4723 > appium.log 2>&1 &
          APPIUM_PID=$!
          echo "Appium started with PID: $APPIUM_PID"
          echo "APPIUM_PID=$APPIUM_PID" >> $GITHUB_ENV
        working-directory: ujet-qa-e2e

      - name: 'Wait for Appium server to start'
        run: |
          echo "Waiting for Appium server to be ready..."
          COUNTER=0
          MAX_ATTEMPTS=30
          while [ $COUNTER -lt $MAX_ATTEMPTS ]; do
            if curl -s http://localhost:4723/status > /dev/null; then
              echo "Appium server is ready!"
              curl -s http://localhost:4723/status || echo "Appium status check failed"
              break
            fi
            echo "Waiting for Appium server... (attempt $((COUNTER + 1))/$MAX_ATTEMPTS)"
            sleep 2
            COUNTER=$((COUNTER + 1))
          done

          if [ $COUNTER -eq $MAX_ATTEMPTS ]; then
            echo "Appium server failed to start within 60 seconds"
            exit 1
          fi
        working-directory: ujet-qa-e2e

      - name: 'Verify setup before tests'
        run: |
          echo "Checking simulator status..."
          xcrun simctl list devices | grep "$SIMULATOR_DEVICE_ID"

          echo "Getting exact iOS version of running simulator..."
          ACTUAL_IOS_VERSION=$(xcrun simctl list devices | grep "$SIMULATOR_DEVICE_ID" | grep -o "iOS [0-9.]*" | head -1)
          echo "Simulator is running: $ACTUAL_IOS_VERSION"

          echo "Checking if target app is installed..."
          xcrun simctl listapps "$SIMULATOR_DEVICE_ID" | grep -i "co.ujet.ios.v3" || echo "Target app not found!"

          echo "Checking Appium server..."
          curl -s http://localhost:4723/status || echo "Appium server not responding"

      - name: 'Run e2e tests'
        env:
          LOG_LEVEL: debug
          TRIGGER_TYPE: Daily
          MOBILE_APP: ENDUSERAPP
          ENDUSER_APP_VERSION: v3
          ENDUSER_APP_IDENTIFIER: qa
          MOBILE_DEVICE: ${{ matrix.device_name }}
          MOBILE_DEVICE_OS_VERSION: ${{ matrix.os_version }}
          DEVICE_OS: ios
          PROJECT_ID: ujet-staging-tst01
          ADMIN_PORTAL_URL: https://ujete2egeneric01.tst01.g.ujetstage.co
          AUTOMATION_INTERNAL_SECRET: ${{ secrets.AUTOMATION_INTERNAL_SECRET }}
          DEFAULT_PASSWORD: ${{ secrets.DEFAULT_PASSWORD }}
          chatbot_key1_json: ${{ secrets.CHATBOT_KEY1_JSON }}
          SIMULATOR_DEVICE_ID: ${{ env.SIMULATOR_DEVICE_ID }}
          MOBILE_DEVICE_UDID: ${{ env.SIMULATOR_DEVICE_ID }}
        run: |
          echo "=== Test Environment Debug ==="
          echo "MOBILE_DEVICE: $MOBILE_DEVICE"
          echo "MOBILE_DEVICE_OS_VERSION: $MOBILE_DEVICE_OS_VERSION"
          echo "SIMULATOR_DEVICE_ID (our prepared simulator): $SIMULATOR_DEVICE_ID"
          echo "MOBILE_DEVICE_UDID (should force Appium to use our simulator): $MOBILE_DEVICE_UDID"
          echo "Bundle ID expected: co.ujet.ios.v3.QaLocal"
          echo ""
          echo "=== Verifying app is on our simulator ==="
          xcrun simctl listapps "$SIMULATOR_DEVICE_ID" | grep "co.ujet.ios.v3.QaLocal" || echo "App not found on our simulator!"
          echo ""
          echo "=== Starting E2E Tests ==="
          npm run mobile:local -- --connectionRetryTimeout=300000 --waitforTimeout=60000 --connectionRetryCount=5 --config=wdio.ci.conf.js
        working-directory: ujet-qa-e2e

      - name: 'Show Appium logs on failure'
        if: cancelled() || failure()
        run: |
          echo "=== Appium Server Logs ==="
          cat appium.log || echo "No Appium log file found"
          echo "=== Appium Process Status ==="
          ps aux | grep appium || echo "No Appium process found"
        working-directory: ujet-qa-e2e
