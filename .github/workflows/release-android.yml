name: Release Android
on:
  workflow_dispatch:
    inputs:
      target:
        description: 'Target'
        required: true
        default: 'qa'
        type: choice
        options:
        - qa
        - rc
        - production
      qa_target_version:
        description: 'Current PI number for qa target and main branch'
        required: false
        type: string
      skip_test:
        description: 'Skip test'
        required: false
        default: false
        type: boolean
      skip_production_deploy:
        description: 'Skip production deploy step'
        required: false
        type: boolean
        default: false
      release_notes:
        description: 'Release Notes'
        required: false
        type: string
jobs:
  # Job to run tests on all targets
  run-test:
    runs-on: macos-15-xlarge
    timeout-minutes: 30

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: gradle-${{ runner.os }}-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            gradle-${{ runner.os }}-

      - name: run tests
        run: ./android/gradlew testReleaseUnitTest jacocoTestReport --max-workers=2 -p android

  # Job to upload app to firebase which runs after tests pass
  release-app:
    needs: run-test
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android

      - name: Decode keystore
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE_BASE64 }}" | base64 -d > release-key.jks

      - name: 'Release test app'
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
          FIREBASE_GROUPS: ${{ vars.FIREBASE_GROUPS_ANDROID }}
          KEYSTORE_FILE: ${{ github.workspace }}/release-key.jks
          KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
          KEY_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
        run: |
          firebase_app_id=$(echo '${{ vars.FIREBASE_APP_ID_ANDROID }}' | jq -r '.${{ github.event.inputs.target }}')

          bundle exec fastlane android release \
            branch:${{ github.ref_name }} \
            target:${{ github.event.inputs.target }} \
            qa_target_version:${{ github.event.inputs.qa_target_version }} \
            release_notes:"${{ github.event.inputs.release_notes }}" \
            firebase_app_id:"${firebase_app_id}"

      - name: Upload App to LambdaTest for E2E
        env:
          LT_USER: ${{ secrets.LT_USER }}
          LT_API_KEY: ${{ secrets.LT_API_KEY }}
        run: |
          bundle exec fastlane run upload_app \
            platform:android \
            target:${{ github.event.inputs.target }}

  # Job to release the RC app and upload artifacts which runs after tests are passed
  rc-deploy:
    needs: run-test
    if: ${{ github.event.inputs.target == 'rc' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android

      - name: Decode keystore
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE_BASE64 }}" | base64 -d > release-key.jks

      - name: 'Configure AWS credentials for uploading SDK packages'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 'Upload SDK into S3'
        env:
          AWS_S3_URI: ${{ vars.AWS_S3_URI }}
        run: |
          bundle exec fastlane run upload_sdk \
            platform:android \
            branch:${{ github.ref_name }} \
            target:${{ github.event.inputs.target }}

  # Job to release the production app and upload artifacts
  production-deploy:
    if: ${{ github.event.inputs.target == 'production' && github.event.inputs.skip_production_deploy == 'false' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v4

      - name: 'Setup Android Environment'
        uses: ./.github/actions/setup-android

      - name: Decode keystore
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE_BASE64 }}" | base64 -d > release-key.jks

      - name: 'Configure AWS credentials for uploading SDK packages'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: 'Copy SDKs from staging to production folder'
        env:
          AWS_S3_URI: ${{ vars.AWS_S3_URI }}
          ARTIFACT_GROUP_ID_ANDROID: ${{ vars.ARTIFACT_GROUP_ID_ANDROID }}
        run: |
          bundle exec fastlane run copy_sdk platform:android
