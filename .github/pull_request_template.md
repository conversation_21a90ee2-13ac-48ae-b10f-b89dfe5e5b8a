https://ujetcs.atlassian.net/browse/

// image or video here

### Summary



### Cause (Bug fixes only)



### Resolution



### Note to Reviewers

// Describe any areas of concern or specific parts of the changes where you would like a deeper review or are not fully confident.

### Checklist

**Requester**: Complete all items before assigning reviewers.
**Reviewers**: Verify all items before approving.

#### General

- [ ] **JIRA Update**: JIRA ticket is up to date (Status, Epic, Sprint, Release Version, Fix Version, QA Test Hint).
- [ ] **PR Check**: All Checks are passed and feedback from <PERSON><PERSON><PERSON> has been addressed.
- [ ] **Documentation**: All public interface changes are documented (DocC for iOS, KDoc for Android).

#### Test

A valid explanation should be provided in the 'Note to Reviewers' section if tests were not added.

- [ ] **Unit Test**: Unit tests added/updated for all new/changed logic (including View Models of UI module).
- [ ] **E2E Test**: E2E tests added for new features (not required for bugfixes).
- [ ] **Test Coverage**: Code coverage is not reduced.
- [ ] **Manual Testing**: Manual testing performed and described above.
- [ ] **Test Explanation**: If tests are not added, a valid explanation is provided in the 'Note to Reviewers' section.

#### UI

- [ ] **Accessibility**: New/updated UI components checked for accessibility (VoiceOver, Larger Text, Dark Mode, Landscape).
- [ ] **Preview**: Previews added for view components.
