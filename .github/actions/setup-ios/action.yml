name: 'Setup iOS Environment'
description: 'Common setup steps for iOS jobs including Ruby setup, dependencies, and caching'

runs:
  using: 'composite'
  steps:
    - name: 'Install gems'
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.3.7'
        bundler-cache: true

    - name: 'Download dependencies'
      run: bundle exec fastlane run download_dependencies
      shell: bash

    - name: 'Cache Swift Packages'
      uses: actions/cache@v3
      with:
        path: /Users/<USER>/work/ujet-mobile-sdk-v3/ujet-mobile-sdk-v3/build/DerivedData/SourcePackages/checkouts
        key: ${{ runner.os }}-spm-${{ hashFiles('**/Package.resolved') }}
        restore-keys: |
          ${{ runner.os }}-spm-
