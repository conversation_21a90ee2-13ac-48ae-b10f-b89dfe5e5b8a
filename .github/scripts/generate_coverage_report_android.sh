#!/bin/bash

# Check if base ref is provided
if [ -z "$1" ]; then
    echo "Error: Base branch reference not provided"
    exit 1
fi

BASE_REF=$1

# Get changed Kotlin files
CHANGED_FILES=$(git diff --name-only origin/$BASE_REF..HEAD -- '*.kt') || true
echo "Changed Kotlin files: $CHANGED_FILES"

# Initialize coverage report
echo "## Code Coverage for Changed Files" > coverage_report.txt
echo "" >> coverage_report.txt

# Process each module's coverage report
for report in $(find . -name "report.xml" -path "*/build/reports/coverage/test/*"); do
    module=$(echo "$report" | cut -d'/' -f3)
    if [ -z "$CHANGED_FILES" ]; then
        echo "No Kotlin files were changed in this module." >> coverage_report.txt
    else
        for file in $CHANGED_FILES; do
            if [[ $file == *"$module"* ]]; then
                # Extract class name from file path
                class_name=$(basename "$file" .kt)
                
                # Use xmllint to extract coverage data for the class
                coverage_data=$(xmllint --xpath "//class[contains(@name,'$class_name')]" "$report" 2>/dev/null)
                
                if [ ! -z "$coverage_data" ]; then
                    # Extract line coverage percentage
                    covered_lines=$(echo "$coverage_data" | xmllint --xpath "sum(//class[contains(@name,'$class_name')]/counter[@type='LINE']/@covered)" - 2>/dev/null)
                    missed_lines=$(echo "$coverage_data" | xmllint --xpath "sum(//class[contains(@name,'$class_name')]/counter[@type='LINE']/@missed)" - 2>/dev/null)
                    
                    total_lines=$((covered_lines + missed_lines))
                    if [ $total_lines -gt 0 ]; then
                        coverage_pct=$(awk "BEGIN { printf \"%.1f\", ($covered_lines / $total_lines) * 100 }")
                        echo "${file}: $covered_lines of $total_lines lines (${coverage_pct}%)" >> coverage_report.txt
                    fi
                fi
            fi
        done
    fi
done

# Calculate overall coverage
echo "" >> coverage_report.txt
total_covered=0
total_lines=0

for report in $(find . -name "report.xml" -path "*/build/reports/coverage/test/*"); do
    covered=$(xmllint --xpath "sum(//counter[@type='LINE']/@covered)" "$report" 2>/dev/null)
    missed=$(xmllint --xpath "sum(//counter[@type='LINE']/@missed)" "$report" 2>/dev/null)
    
    total_covered=$((total_covered + covered))
    total_lines=$((total_lines + covered + missed))
done

if [ $total_lines -gt 0 ]; then
    overall_coverage=$(awk "BEGIN { printf \"%.1f\", ($total_covered / $total_lines) * 100 }")
    echo "Test Coverage: ${overall_coverage}% ($total_covered of $total_lines lines)" >> coverage_report.txt
fi
