#!/bin/bash

# Check if base ref is provided
if [ -z "$1" ]; then
    echo "Error: Base branch reference not provided"
    exit 1
fi

BASE_REF=$1

# Get changed Swift files
CHANGED_FILES=$(git diff --name-only origin/$BASE_REF..HEAD -- '*.swift') || true

# Run slather and extract coverage for changed files
bundle exec slather coverage > full_coverage.txt

# Filter and format coverage for changed files only
echo "## Code Coverage for Changed Files" > coverage_report.txt
echo "" >> coverage_report.txt

if [ -z "$CHANGED_FILES" ]; then
    echo "No Swift files were changed in this PR." >> coverage_report.txt
else
    while IFS= read -r file; do
        grep "$file:" full_coverage.txt >> coverage_report.txt || true
    done <<< "$CHANGED_FILES"
fi

echo "" >> coverage_report.txt
grep "Test Coverage:" full_coverage.txt >> coverage_report.txt || echo "Unable to determine overall test coverage" >> coverage_report.txt