package com.ccaiplatform.ccaichat.interfaces

/**
 * Represents network-related errors that can occur during chat operations.
 */
sealed class ChatNetworkError : Throwable() {
    /**
     * Indicates that the chat is invalid.
     */
    data object InvalidChat : ChatNetworkError()

    /**
     * Indicates that the current chat ID is invalid.
     */
    data object InvalidCurrentChatId : ChatNetworkError()

    /**
     * Indicates that the photo list is empty.
     */
    data object EmptyPhotos : ChatNetworkError()

    /**
     * Indicates that the video list is empty.
     */
    data object EmptyVideos : ChatNetworkError()

    /**
     * Indicates that the task VA message is invalid.
     */
    data object InvalidTaskVaMessage : ChatNetworkError()

    /**
     * Indicates that the custom form answer is invalid.
     */
    data object InvalidCustomFormAnswer : ChatNetworkError()

    /**
     * Indicates that the web form interface is invalid.
     */
    data object InvalidWebFormInterface : ChatNetworkError()
}
