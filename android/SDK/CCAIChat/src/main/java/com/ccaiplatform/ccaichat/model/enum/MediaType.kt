package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the type of media that can be sent in the chat.
 */
@JsonAdapter(MediaTypeAdapter::class)
enum class MediaType {
    /** Photo media type. */
    Photo,

    /** Screenshot media type. */
    Screenshot;

    companion object {
        /**
         * Returns the [MediaType] corresponding to the given string value, case-insensitive.
         *
         * @param value The string value of the media type.
         * @return The matching [MediaType], or null if not found.
         */
        fun fromValue(value: String): MediaType? {
            return entries.find { it.name.lowercase() == value.lowercase() }
        }
    }
}

/**
 * <PERSON><PERSON> type adapter for serializing and deserializing [MediaType].
 *
 * This adapter converts between the enum's string value (lowercase) and the enum instance
 * when reading from or writing to JSON.
 */
class MediaTypeAdapter : JsonSerializer<MediaType>, JsonDeserializer<MediaType> {

    override fun serialize(
        src: MediaType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.name?.lowercase() ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): MediaType? {
        return MediaType.fromValue(json?.asString ?: "")
    }
}
