package com.ccaiplatform.ccaichat.model.enum

import androidx.annotation.IntDef

/**
 * Annotation for defining supported document types.
 *
 * Use this annotation to restrict allowed integer values to the defined document types.
 */
@Retention(AnnotationRetention.SOURCE)
@IntDef(
    DocumentType.GENERIC, DocumentType.PDF, DocumentType.EXCEL,
    DocumentType.DOC, DocumentType.PPT, DocumentType.AUDIO, DocumentType.VIDEO,
)
annotation class DocumentType {
    companion object {
        /** Generic document type. */
        const val GENERIC = 1

        /** PDF document type. */
        const val PDF = 2

        /** Excel document type. */
        const val EXCEL = 3

        /** Word document type. */
        const val DOC = 4

        /** PowerPoint document type. */
        const val PPT = 5

        /** Audio file type. */
        const val AUDIO = 6

        /** Video file type. */
        const val VIDEO = 7
    }
}
