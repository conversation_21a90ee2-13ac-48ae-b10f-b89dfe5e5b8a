package com.ccaiplatform.ccaichat.model.enum

import java.text.SimpleDateFormat
import java.util.Locale

/**
 * Represents an answer to a custom form field.
 */
sealed class CustomFormAnswer {

    /**
     * Text answer to a form field.
     *
     * @property content The text content of the answer.
     */
    data class Text(val content: String) : CustomFormAnswer()

    /**
     * Option answer to a form field, representing selected positions and their content.
     *
     * @property answerOfPositions The list of selected option positions.
     * @property content The content of the selected options.
     */
    data class Option(val answerOfPositions: List<Int>, val content: String) : CustomFormAnswer()

    /**
     * Date answer to a form field.
     *
     * @property date The selected date.
     */
    data class Date(val date: java.util.Date) : CustomFormAnswer()

    /**
     * Time answer to a form field.
     *
     * @property date The selected time.
     */
    data class Time(val date: java.util.Date) : CustomFormAnswer()

    /**
     * Toggle (boolean) answer to a form field.
     *
     * @property isOn Whether the toggle is on or off.
     */
    data class Toggle(val isOn: Boolean) : CustomFormAnswer()

    /**
     * Represents no answer.
     */
    data object None : CustomFormAnswer()

    /**
     * Returns the string representation of the answer, or null if none.
     */
    val stringValue: String?
        get() = when (this) {
            is Text -> content.trim()
            is Option -> content
            is Date -> SimpleDateFormat("MM/dd/yyyy", Locale.US).format(date)
            is Time -> SimpleDateFormat("HH:mm", Locale.US).format(date)
            is Toggle -> if (isOn) "true" else "false"
            None -> null
        }

    /**
     * Checks if the answer is valid (non-empty string value).
     */
    val isValid get() = stringValue.orEmpty().trim().isNotEmpty()
}
