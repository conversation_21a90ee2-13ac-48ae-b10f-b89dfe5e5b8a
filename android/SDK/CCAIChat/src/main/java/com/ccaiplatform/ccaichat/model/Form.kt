package com.ccaiplatform.ccaichat.model

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import java.lang.reflect.Type

@JsonAdapter(FormDeserializer::class)
sealed class Form {

    abstract val name: String?
    abstract var title: String?
    abstract val subtitle: String?
    abstract val smartActionId: Int?
    abstract val image: String?

    data class WebForm(
        @SerializedName("name")
        override val name: String? = null,

        @SerializedName("title")
        override var title: String? = null,

        @SerializedName("subtitle")
        override val subtitle: String? = null,

        @SerializedName("preview_endpoint")
        val previewEndpoint: String? = null,

        @SerializedName("external_form_id")
        val externalFormId: String? = null,

        @SerializedName("smart_action_id")
        override val smartActionId: Int? = null,

        @SerializedName("image")
        override val image: String? = null,
    ) : Form()

    data class CustomForm(
        @SerializedName("id")
        val id: Int? = null,

        @SerializedName("name")
        override val name: String? = null,

        @SerializedName("title")
        override var title: String? = null,

        @SerializedName("subtitle")
        override val subtitle: String? = null,

        @SerializedName("smart_action_id")
        override val smartActionId: Int? = null,

        @SerializedName("image")
        override val image: String? = null,

        @SerializedName("header")
        val header: String? = null,

        @SerializedName("footer")
        val footer: String? = null,
    ) : Form()

    var completed: Boolean = false
}


class FormDeserializer : JsonDeserializer<Form> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Form? {
        val jsonObject = json?.asJsonObject ?: return null

        return if (jsonObject.has("form_type")
            && jsonObject.getAsJsonPrimitive("form_type").asString == "custom"
        ) {
            context?.deserialize(jsonObject, Form.CustomForm::class.java)
        } else {
            context?.deserialize(jsonObject, Form.WebForm::class.java)
        }
    }
}
