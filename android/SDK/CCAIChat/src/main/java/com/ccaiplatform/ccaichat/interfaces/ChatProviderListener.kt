package com.ccaiplatform.ccaichat.interfaces

import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent

/**
 * Listener interface for receiving chat provider events such as messages, typing, member changes,
 * and state updates.
 */
interface ChatProviderListener {
    /**
     * Called when new chat messages are received.
     *
     * @param messages The list of received chat messages.
     */
    fun onMessagesReceived(messages: List<ChatMessage>)

    /**
     * Called when a typing event occurs in the chat.
     *
     * @param typingEvent The typing event information.
     */
    fun onTypingEvent(typingEvent: ChatTypingEvent)

    /**
     * Called when a member joins or leaves the chat.
     *
     * @param memberEvent The member event information.
     */
    fun onMemberEvent(memberEvent: ChatMemberEvent)

    /**
     * Called when the chat provider state changes.
     *
     * @param state The new state of the chat provider.
     */
    fun onStateChanged(state: Chat<PERSON>roviderState)

    /**
     * Called with the result of a message send operation.
     *
     * @param state True if the message was sent successfully, false otherwise.
     */
    fun onMessageSendResult(state: <PERSON>olean)
}
