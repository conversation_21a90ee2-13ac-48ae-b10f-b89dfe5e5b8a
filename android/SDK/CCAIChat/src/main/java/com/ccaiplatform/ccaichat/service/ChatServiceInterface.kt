package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.interfaces.ChatNetworkError
import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatHistoryInfo
import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderError
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatTransferEvent
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import com.ccaiplatform.ccaichat.model.enum.OutgoingMessageContent
import com.ccaiplatform.ccaikit.models.SmartAction
import kotlinx.coroutines.flow.SharedFlow
import java.io.File

interface ChatServiceInterface {
    /**
     * Emits lists of chat messages received in the current chat session.
     */
    val messagesReceivedSubject: SharedFlow<List<ChatMessage>>

    /**
     * Emits typing events indicating when a user or agent is typing.
     */
    val typingEventSubject: SharedFlow<ChatTypingEvent>

    /**
     * Emits member events such as users joining or leaving the chat.
     */
    val memberEventSubject: SharedFlow<ChatMemberEvent>

    /**
     * Emits transfer events related to chat handoff or transfer actions.
     */
    val transferEventSubject: SharedFlow<ChatTransferEvent>

    /**
     * Emits state changes of the chat provider (e.g., connected, disconnected).
     */
    val stateChangedSubject: SharedFlow<ChatProviderState>

    /**
     * Emits chat response data when a chat is received or updated.
     */
    val chatReceivedSubject: SharedFlow<ChatResponse>

    /**
     * Emits status check results as a map of key-value pairs.
     */
    val checkStatusSubject: SharedFlow<Map<String, Any>>

    /**
     * Emits the result of sending a message (true if successful, false otherwise).
     */
    val messageSendResultSubject: SharedFlow<Boolean>

    /**
     * Starts a new chat session with the specified menu ID.
     * This function will create a new chat and connect to the chat provider.
     *
     * @param menuId The ID of the menu to start the chat with
     * @throws ChatProviderError.FailedToGetChat If there's an error creating the chat
     * @throws Exception If there's an error creating the chat
     */
    suspend fun start(menuId: Int)

    /**
     * Resumes an existing chat session.
     * If the chat is already connected with the same provider, it will just rejoin.
     * Otherwise, it will create a new connection with the appropriate provider.
     *
     * @param chat The chat response containing the chat details to resume
     * @throws ChatProviderError.FailedToGetChat If no provider is available for the chat
     */
    suspend fun resume(chat: ChatResponse)

    /**
     * Enqueues the current chat session.
     * This will put the current chat in a queue for processing.
     *
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun enqueueCurrentChat()

    /**
     * Retrieves the last chat that is still in progress.
     *
     * @return The last chat in progress, or null if no chat is in progress
     */
    suspend fun getLastChatInProgress(): ChatResponse?

    /**
     * Sends a message in the current chat session.
     *
     * @param message The message content to send
     * @throws ChatNetworkError.InvalidTaskVaMessage If task VA message is invalid
     */
    suspend fun sendMessage(message: OutgoingMessageContent)

    /**
     * Retrieves a task VA message by its ID.
     *
     * @param messageId The ID of the message to retrieve
     * @return The task VA message, or null if not found
     */
    suspend fun getTaskVaMessage(messageId: Int?): ChatTaskVaMessage?

    /**
     * Sends a task VA message in the current chat session.
     *
     * @param message The message content to send
     * @return The sent task VA message, or null if sending failed
     */
    suspend fun sendTaskVaMessage(message: OutgoingMessageContent): ChatTaskVaMessage?

    /**
     * Uploads photos to the current chat session.
     *
     * @param photos List of photo byte arrays to upload
     * @param smartAction Optional smart action associated with the upload
     * @param contentType Optional contentType that provides photo mimeType for the upload.
     * @return List of photo IDs after successful upload
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun uploadPhotos(photos: List<ByteArray>, smartAction: SmartAction?, contentType: String?): List<Int>

    /**
     * Updates the typing status in the current chat session.
     *
     * @param isTyping Whether the user is currently typing
     */
    fun typing(isTyping: Boolean)

    /**
     * Sends a message preview to the web while the user is typing.
     *
     * @param messagePreview The message preview string to send
     */
    fun sendMessagePreview(messagePreview: String)

    /**
     * Ends the current chat session.
     *
     * @return true if the chat was successfully ended, false otherwise
     */
    suspend fun endChat(): Boolean

    /**
     * Escalates the current chat to a human agent.
     *
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun escalateToHumanAgent()

    /**
     * Checks the current status of the chat session.
     *
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun checkStatus()

    /**
     * Gets the URL for a web form.
     *
     * @param externalFormId The ID of the external form
     * @param smartActionId The ID of the smart action
     * @return The web form response containing the form URL
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun getFormUrl(externalFormId: String, smartActionId: Int): WebFormResponse

    /**
     * Verifies the submitted form data.
     *
     * @param formData The form data to verify
     * @return true if verification was successful, false otherwise
     */
    suspend fun verifyFormData(formData: WebFormResponse): Boolean

    /**
     * Gets the chat history.
     *
     * @param page The page number for pagination
     * @param perPage Number of items per page
     * @return The chat history information, or null if retrieval failed
     */
    suspend fun getPreviousChats(page: Int, perPage: Int): ChatHistoryInfo?

    /**
     * Gets all task VA messages for the current chat.
     *
     * @return List of task VA messages, or null if retrieval failed
     */
    suspend fun getTaskVaMessages(): List<ChatMessage>?

    /**
     * Escalates the chat with deflection to a specific channel.
     *
     * @param escalationId The ID of the escalation
     * @param deflectionChannel The channel to deflect to
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun escalate(escalationId: Int, deflectionChannel: String)

    /**
     * Sends an event to the current chat session.
     *
     * @param event The chat event to send
     * @throws ChatNetworkError.InvalidCurrentChatId If no chat is currently active
     */
    suspend fun sendEvent(event: ChatEvent)

    /**
     * Retrieves the details of a custom form based on the provided form ID.
     *
     * @param formId The unique identifier of the custom form to retrieve.
     * @return A [CustomFormDetailsResponse] containing the details of the custom form,
     * or `null` if the form is not found.
     */
    suspend fun getCustomFormDetails(formId: Int): CustomFormDetailsResponse?

    /**
     * Submits a custom form with the provided request data.
     *
     * @param request The custom form submission request
     * @return true if submission was successful, false otherwise
     */
    suspend fun submitCustomForm(request: SubmitCustomFormRequest): Boolean

    /**
     * Downloads the PDF transcript file for the current chat session.
     * @return The downloaded PDF file, or null if download failed.
     */
    suspend fun downloadChatTranscript(): File?
}
