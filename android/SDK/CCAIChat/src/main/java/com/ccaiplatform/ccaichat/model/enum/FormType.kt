package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the type of a form in the chat application.
 *
 * @property value The string value associated with the form type.
 */
@JsonAdapter(FormTypeAdapter::class)
enum class FormType(val value: String) {
    /** Web-based form type. */
    Web("web"),

    /** Custom form type. */
    Custom("custom");

    companion object {
        /**
         * Returns the [FormType] corresponding to the given string value.
         *
         * @param value The string value of the form type.
         * @return The matching [FormType], or [Web] if not found.
         */
        fun fromValue(value: String): FormType {
            return entries.find { it.value == value } ?: Web
        }
    }
}

/**
 * Gson type adapter for serializing and deserializing [FormType].
 *
 * This adapter converts between the enum's string value and the enum instance
 * when reading from or writing to JSON.
 */
class FormTypeAdapter : JsonSerializer<FormType>, JsonDeserializer<FormType> {

    override fun serialize(
        src: FormType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.value ?: "web")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): FormType {
        return FormType.fromValue(json?.asString ?: "web")
    }
}
