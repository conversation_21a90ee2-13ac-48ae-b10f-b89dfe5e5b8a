package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents possible errors that can occur during media upload.
 */
sealed class MediaUploadError : Error() {
    /** Error indicating the file is too large to upload. */
    object FileTooLarge : MediaUploadError()

    /** Error indicating access to the file or upload is denied. */
    object AccessDenied : MediaUploadError()

    /**
     * Generic upload error with an optional error code.
     *
     * @property code The error code associated with the upload failure.
     */
    class UploadError(val code: Int = 0) : MediaUploadError()
}
