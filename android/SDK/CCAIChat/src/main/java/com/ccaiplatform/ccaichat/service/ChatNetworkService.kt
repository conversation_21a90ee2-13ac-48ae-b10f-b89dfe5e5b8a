package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.model.ChatAPIEndpoint
import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatHistoryResponse
import com.ccaiplatform.ccaichat.model.ChatMessageRequest
import com.ccaiplatform.ccaichat.model.ChatRequest
import com.ccaiplatform.ccaichat.model.ChatRequestParameter
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaichat.model.ChatToken
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.VerifyFormDataRequestParameter.VERIFY_PATH
import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaichat.model.enum.ChatStatus
import com.ccaiplatform.ccaikit.models.SmartAction
import com.ccaiplatform.ccaikit.models.response.HttpExceptionWithCode
import com.ccaiplatform.ccaikit.models.response.transcript.GenerateChatTranscriptResponse
import com.ccaiplatform.ccaikit.services.network.HttpMethod
import com.ccaiplatform.ccaikit.services.network.NetworkRequest
import com.ccaiplatform.ccaikit.services.network.NetworkService
import com.ccaiplatform.ccaikit.util.FileUtil.copyToFile
import okhttp3.Response
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Implementation of [ChatNetworkServiceInterface] for handling network operations related to chat.
 *
 * @property networkService Service for network communication related to chat.
 * @property deviceId The unique device identifier used in requests.
 * @property language The language code for chat operations.
 */
class ChatNetworkService : ChatNetworkServiceInterface {
    internal val networkService: NetworkService
    private val deviceId: String
    private val language: String

    /**
     * Constructs a [ChatNetworkService] with the given dependencies.
     *
     * @param networkService The network service to use for requests.
     * @param deviceId The device identifier.
     * @param language The language code.
     */
    constructor(networkService: NetworkService, deviceId: String, language: String) {
        this.networkService = networkService
        this.deviceId = deviceId
        this.language = language
    }

    /**
     * Creates a new chat session for the given menu ID.
     *
     * @param menuId The menu ID to start the chat with.
     * @return A [Result] containing the created [ChatResponse].
     */
    override suspend fun createChat(menuId: Int): Result<ChatResponse> {
        val chatRequest = ChatRequest(menuId = menuId, languageCode = language)
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.CHAT,
            body = chatRequest
        )
        return runCatching {
            networkService.sendRequest<ChatResponse>(request = apiRequest)
        }
    }

    /**
     * Retrieves a chat token for the specified chat and provider type.
     *
     * @param chatId The chat session ID.
     * @param providerType The provider type.
     * @return A [Result] containing the chat token string.
     */
    override suspend fun getToken(chatId: Int, providerType: String): Result<String> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.chatToken(chatId),
            body = mapOf(
                ChatRequestParameter.DEVICE_ID to deviceId,
                ChatRequestParameter.PROVIDER_TYPE to providerType
            )
        )
        return runCatching {
            networkService.sendRequest<ChatToken>(request = apiRequest).token
        }
    }

    /**
     * Fetches chat details by chat ID.
     *
     * @param id The chat ID.
     * @return A [Result] containing the [ChatResponse].
     */
    override suspend fun getChat(id: Int): Result<ChatResponse> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.chatId(id),
            queryParameters = mutableMapOf("chatId" to id)
        )
        return runCatching {
            networkService.sendRequest<ChatResponse>(request = apiRequest)
        }
    }

    /**
     * Verifies the provided web form data.
     *
     * @param formData The web form response to verify.
     * @return A [Result] indicating success or failure.
     */
    override suspend fun verifyFormData(formData: WebFormResponse): Result<Unit> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = VERIFY_PATH,
            body = formData,
        )
        return runCatching {
            networkService.sendRequest<WebFormResponse>(request = apiRequest)
        }
    }

    /**
     * Marks the chat as finished.
     *
     * @param id The chat ID.
     * @return A [Result] indicating success or failure.
     */
    override suspend fun finishChat(id: Int): Result<Unit> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.PATCH,
            path = ChatAPIEndpoint.chatId(id),
            body = mapOf(ChatRequestParameter.STATUS to ChatStatus.Finished.rawValue)
        )
        return runCatching {
            networkService.sendRequest<Unit>(request = apiRequest)
        }
    }

    /**
     * Enqueues the chat for processing.
     *
     * @param id The chat ID.
     * @return A [Result] containing the updated [ChatResponse].
     */
    override suspend fun enqueue(id: Int): Result<ChatResponse> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.PATCH,
            path = ChatAPIEndpoint.chatId(id),
            body = mapOf(ChatRequestParameter.STATUS to ChatStatus.Queued.rawValue)
        )
        return runCatching {
            networkService.sendRequest<ChatResponse>(request = apiRequest)
        }
    }

    /**
     * Retrieves a Task Virtual Agent message by chat and message ID.
     *
     * @param chatId The chat ID.
     * @param messageId The message ID (optional).
     * @return A [Result] containing the [ChatTaskVaMessage].
     */
    override suspend fun getTaskVaMessage(chatId: Int, messageId: Int?): Result<ChatTaskVaMessage> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.taskVaMessage(chatId, messageId),
        )
        return runCatching {
            networkService.sendRequest<ChatTaskVaMessage>(request = apiRequest)
        }
    }

    /**
     * Sends a Task Virtual Agent message.
     *
     * @param chatId The chat ID.
     * @param message The message request.
     * @return A [Result] containing the sent [ChatTaskVaMessage].
     */
    override suspend fun sendTaskVaMessage(chatId: Int, message: ChatMessageRequest): Result<ChatTaskVaMessage> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.taskVaMessage(chatId),
            body = mapOf(
                "content" to message
            )
        )
        return runCatching {
            networkService.sendRequest<ChatTaskVaMessage>(request = apiRequest)
        }
    }

    /**
     * Retrieves all chats with status in progress.
     *
     * @return A [Result] containing a list of [ChatResponse].
     */
    override suspend fun getChatsFilterInProgress(): Result<List<ChatResponse>> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.CHAT,
            queryParameters = mutableMapOf(
                "status[]" to ChatStatus.inProgressStatus.map { it.rawValue }
            )
        )
        return runCatching {
            networkService.sendRequest<List<ChatResponse>>(request = apiRequest)
        }
    }

    /**
     * Escalates the chat to a human agent.
     *
     * @param chatId The chat ID.
     */
    override suspend fun escalateToHumanAgent(chatId: Int) {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.chatEscalation(chatId),
        )
        networkService.sendRequest<Unit>(request = apiRequest)
    }

    /**
     * Uploads photos for the specified chat.
     *
     * @param chatId The chat ID.
     * @param photos The list of photo byte arrays.
     * @param type The type of upload.
     * @param smartAction Optional smart action context.
     * @param contentType Optional contentType of photo mimeType with the upload.
     * @return A [Result] containing a list of uploaded photo IDs.
     */
    override suspend fun uploadPhotos(chatId: Int, photos: List<ByteArray>, type: String, smartAction: SmartAction?, contentType: String?): Result<List<Int>> {
        return uploadPhotosInternal(chatId, photos, type, smartAction,contentType)
    }

    /**
     * Retrieves chat history messages with pagination.
     *
     * @param page The page number.
     * @param perPage The number of items per page.
     * @return A [Result] containing [ChatHistoryResponse].
     */
    override suspend fun getHistoryMessages(page: Int, perPage: Int): Result<ChatHistoryResponse> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.CHAT_HISTORY,
            queryParameters = mutableMapOf(ChatRequestParameter.PAGE to page, ChatRequestParameter.PER_PAGE to perPage)
        )
        return runCatching {
            networkService.sendRequest<ChatHistoryResponse>(apiRequest)
        }
    }

    /**
     * Retrieves Task Virtual Agent messages for a chat.
     *
     * @param chatId The chat ID.
     * @return A [Result] containing a list of [ChatTaskVaMessage].
     */
    override suspend fun getTaskVaMessages(chatId: Int): Result<List<ChatTaskVaMessage>> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.taskVaMessage(chatId),
        )
        return runCatching {
            networkService.sendRequest<List<ChatTaskVaMessage>>(apiRequest)
        }
    }

    /**
     * Escalates the chat with a deflection channel.
     *
     * @param chatId The chat ID.
     * @param escalationId The escalation ID.
     * @param deflectionChannel The deflection channel.
     */
    override suspend fun escalateChatWithDeflection(chatId: Int, escalationId: Int, deflectionChannel: String) {
        val apiRequest = NetworkRequest(
            method = HttpMethod.PUT,
            path = ChatAPIEndpoint.escalate(chatId, escalationId),
            body = mapOf(
                ChatRequestParameter.DEFLECTION_CHANNEL to deflectionChannel
            )
        )
        networkService.sendRequest<Unit>(apiRequest)
    }

    /**
     * Posts a chat event to the server.
     *
     * @param chatId The chat ID.
     * @param event The chat event.
     */
    override suspend fun postChatEvent(chatId: Int, event: ChatEvent) {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.chatEvent(chatId),
            body = mapOf(ChatRequestParameter.EVENT to event)
        )
        networkService.sendRequest<Unit>(apiRequest)
    }

    /**
     * Retrieves custom form details by form ID.
     *
     * @param formId The form ID.
     * @return A [Result] containing [CustomFormDetailsResponse].
     */
    override suspend fun getCustomFormDetails(formId: Int): Result<CustomFormDetailsResponse> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.customFormDetails(formId),
        )
        return runCatching {
            networkService.sendRequest<CustomFormDetailsResponse>(apiRequest)
        }
    }

    /**
     * Submits a custom form request.
     *
     * @param request The custom form request.
     * @return A [Result] indicating the result of the submission.
     */
    override suspend fun submitCustomForm(request: SubmitCustomFormRequest): Result<Any> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.submitCustomForm(),
            body = request
        )
        return runCatching {
            networkService.sendRequest<Any>(apiRequest)
        }
    }

    /**
     * Generates a PDF transcript file for the chat.
     *
     * @param chatId The ID of the chat session.
     * @return A [Result] containing the [GenerateChatTranscriptResponse].
     */
    override suspend fun generateChatTranscript(chatId: Int): Result<GenerateChatTranscriptResponse> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.POST,
            path = ChatAPIEndpoint.generateChatTranscript(chatId),
        )
        return runCatching {
            networkService.sendRequest<GenerateChatTranscriptResponse>(apiRequest)
        }
    }

    /**
     * Downloads the chat PDF transcript file.
     *
     * @param chatTranscriptId The ID of the chat transcript file.
     * @return A [Result] containing the downloaded file.
     */
    override suspend fun downloadChatTranscript(chatTranscriptId: Int): Result<File> {
        val apiRequest = NetworkRequest(
            method = HttpMethod.GET,
            path = ChatAPIEndpoint.downloadChatTranscript(chatTranscriptId),
        )
        return runCatching {
            val response = networkService.sendRequest<Response>(apiRequest)
            if (response.headers["content-type"]?.contains("application/pdf") != true) throw HttpExceptionWithCode(response.code, "")

            val inputStream = response.body?.byteStream()
            val cacheDirectory = File(networkService.context.cacheDir, "ChatTranscript")
            if (cacheDirectory.exists().not()) {
                cacheDirectory.mkdir()
            }

            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val dateString = dateFormat.format(Date())
            val fileName = "Chat Transcript with ${networkService.companyName} on $dateString"
            val cacheFile = File(cacheDirectory, fileName)
            inputStream?.copyToFile(cacheFile)
            cacheFile
        }
    }
}
