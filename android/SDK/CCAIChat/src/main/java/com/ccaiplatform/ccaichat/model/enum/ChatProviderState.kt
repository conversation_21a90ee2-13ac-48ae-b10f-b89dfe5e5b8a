package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents the various states of a chat provider connection.
 */
sealed class ChatProviderState {
    /**
     * No connection state.
     */
    data object None : ChatProviderState()

    /**
     * The chat provider is currently connecting.
     */
    data object Connecting : ChatProviderState()

    /**
     * The chat provider is connected.
     */
    data object Connected : ChatProviderState()

    /**
     * The chat provider is disconnected.
     */
    data object Disconnected : ChatProviderState()

    /**
     * The chat provider has been deleted.
     */
    data object Deleted : ChatProviderState()

    /**
     * The chat provider encountered an error.
     *
     * @param error The error type.
     * @param description Optional error description.
     */
    data class Error(val error: ChatProviderError, val description: String?) : ChatProviderState()
}
