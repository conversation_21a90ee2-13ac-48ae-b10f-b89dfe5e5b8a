package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.CCAIWebFormInterface
import com.ccaiplatform.ccaichat.interfaces.ChatNetworkError
import com.ccaiplatform.ccaichat.interfaces.ChatProviderInterface
import com.ccaiplatform.ccaichat.interfaces.ChatProviderListener
import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatHistoryInfo
import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.Form
import com.ccaiplatform.ccaichat.model.MediaChatMessageRequest
import com.ccaiplatform.ccaichat.model.PlainTextChatMessageRequest
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.WebFormCompleteMessageRequest
import com.ccaiplatform.ccaichat.model.WebFormRequest
import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaichat.model.TaskVaMessageRequest
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaichat.model.enum.ChatMessageEvent
import com.ccaiplatform.ccaichat.model.enum.ChatProviderError
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatServiceError
import com.ccaiplatform.ccaichat.model.enum.ChatTransferEvent
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import com.ccaiplatform.ccaichat.model.enum.OutgoingMessageContent
import com.ccaiplatform.ccaikit.CCAI
import com.ccaiplatform.ccaikit.models.SmartAction
import com.ccaiplatform.ccaikit.models.logger.LogLevel
import com.ccaiplatform.ccaikit.models.response.communication.Agent
import com.ccaiplatform.ccaikit.util.logging.LoggingUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.collections.filter

/**
 * Service implementation for managing chat sessions, messages, events, and forms.
 *
 * Handles chat lifecycle, sending messages, selecting provider, and communicating with network and provider services.
 *
 * @property networkService Service for network communication related to chat.
 * @property providerService Service for managing chat providers.
 * @property webFormInterface Optional interface for handling web form requests.
 */
class ChatService(
    private val networkService: ChatNetworkServiceInterface,
    private val providerService: ChatProviderServiceInterface,
    private val webFormInterface: CCAIWebFormInterface? = null,
) : ChatServiceInterface, ChatProviderListener {
    /**
     * The currently selected chat provider.
     */
    private var selectedProvider: ChatProviderInterface? = null

    /**
     * The current chat session ID.
     */
    private var currentChatId: Int? = null

    /**
     * The last received chat response.
     */
    private var lastResponse: ChatResponse? = null

    /**
     * Indicates if a Task Virtual Agent is active.
     */
    private var isTaskVaActive = false

    /**
     * Indicates if a virtual agent is currently responding.
     */
    private var isVirtualAgentResponding = false

    /** Emits received chat messages. */
    override val messagesReceivedSubject = MutableSharedFlow<List<ChatMessage>>()

    /** Emits typing events. */
    override val typingEventSubject = MutableSharedFlow<ChatTypingEvent>()

    /** Emits member events. */
    override val memberEventSubject = MutableSharedFlow<ChatMemberEvent>()

    /** Emits transfer events. */
    override val transferEventSubject = MutableSharedFlow<ChatTransferEvent>()

    /** Emits provider state changes. */
    override val stateChangedSubject = MutableSharedFlow<ChatProviderState>()

    /** Emits received chat responses. */
    override val chatReceivedSubject = MutableSharedFlow<ChatResponse>()

    /** Emits status check results. */
    override val checkStatusSubject = MutableSharedFlow<Map<String, Any>>()

    /** Emits message send results. */
    override val messageSendResultSubject = MutableSharedFlow<Boolean>()

    companion object {
        private const val CHAT_TRANSCRIPT_MAX_RETRY_COUNT = 5
        private const val CHAT_TRANSCRIPT_DELAY_INTERVAL = 3000L
    }

    /**
     * Handles received chat messages and emits relevant events.
     *
     * @param messages List of received chat messages.
     */
    override fun onMessagesReceived(messages: List<ChatMessage>) {
        CoroutineScope(Dispatchers.IO).launch {
            val filteredMessages = checkFormCompletedStatus(messages)
            for (message in filteredMessages) {
                if (message.body.type == ChatMessageBodyType.ServerMessage) {
                    val messageBody = message.body
                    val chatTaskVaMessage = getTaskVaMessage(messageBody.messageId)
                    chatTaskVaMessage?.convertToChatMessage()?.let { chatMessage ->
                        messagesReceivedSubject.emit(listOf(chatMessage))
                    }
                } else {
                    if (message.body.type == ChatMessageBodyType.Notification) {
                        when (message.body.event) {
                            ChatMessageEvent.TaskVaStarted -> isTaskVaActive = true
                            ChatMessageEvent.TaskVaFinished -> isTaskVaActive = false
                            ChatMessageEvent.TransferStarted -> transferEventSubject.emit(
                                ChatTransferEvent.Started(
                                    (message.body.agent ?: message.body.fromAgent)?.agentIdString
                                )
                            )

                            ChatMessageEvent.TransferAccepted -> transferEventSubject.emit(
                                ChatTransferEvent.Accepted(
                                    (message.body.agent ?: message.body.fromAgent)?.agentIdString
                                )
                            )

                            ChatMessageEvent.TransferFailed -> transferEventSubject.emit(
                                ChatTransferEvent.Failed(
                                    (message.body.agent ?: message.body.fromAgent)?.agentIdString
                                )
                            )

                            else -> {}
                        }
                    }
                    if (message.author?.startsWith("virtual_agent") == true) {
                        if (isVirtualAgentResponding) {
                            typingEventSubject.emit(ChatTypingEvent.Started(message.author ?: ""))
                            delay(1000)
                            typingEventSubject.emit(ChatTypingEvent.Ended(message.author ?: ""))
                        } else {
                            isVirtualAgentResponding = true
                        }

                    }
                    messagesReceivedSubject.emit(listOf(message))
                }
            }
        }
    }

    /**
     * Handles typing events from the provider.
     *
     * @param typingEvent The typing event.
     */
    override fun onTypingEvent(typingEvent: ChatTypingEvent) {
        CoroutineScope(Dispatchers.IO).launch {
            typingEventSubject.emit(typingEvent)
        }
    }

    /**
     * Handles member events from the provider.
     *
     * @param memberEvent The member event.
     */
    override fun onMemberEvent(memberEvent: ChatMemberEvent) {
        CoroutineScope(Dispatchers.IO).launch {
            memberEventSubject.emit(memberEvent)
        }
    }

    /**
     * Handles provider state changes.
     *
     * @param state The new provider state.
     */
    override fun onStateChanged(state: ChatProviderState) {
        CoroutineScope(Dispatchers.IO).launch {
            stateChangedSubject.emit(state)
        }
    }

    /**
     * Handles the result of a message send operation.
     *
     * @param state True if the message was sent successfully, false otherwise.
     */
    override fun onMessageSendResult(state: Boolean) {
        CoroutineScope(Dispatchers.IO).launch {
            messageSendResultSubject.emit(state)
        }
    }

    /**
     * Connects to a chat provider for the given chat response.
     *
     * @param chat The chat response to connect with.
     */
    private suspend fun connect(chat: ChatResponse) {
        val provider = providerService.providerForResponse(chat, networkService) ?: throw ChatServiceError.FailedToGetProvider
        this.selectedProvider = provider
        this.selectedProvider?.setListener(this)
        provider.join()
        this.currentChatId = chat.id
        this.lastResponse = chat
        chatReceivedSubject.emit(chat)
        checkStatusSubject.emit(mapOf("chat" to chat, "check" to true))
    }

    /**
     * Starts a new chat session with the given menu ID.
     *
     * @param menuId The menu ID to start the chat with.
     */
    override suspend fun start(menuId: Int) {
        stateChangedSubject.emit(ChatProviderState.Connecting)
        networkService.createChat(menuId).onSuccess {
            connect(it)
        }.onFailure { e ->
            LoggingUtil.log("Error creating chat: ${e.message}", LogLevel.ERROR)
            stateChangedSubject.emit(ChatProviderState.Error(ChatProviderError.FailedToGetChat, e.message ?: ""))
            throw e
        }
    }

    /**
     * Resumes an existing chat session.
     *
     * @param chat The chat response to resume.
     */
    override suspend fun resume(chat: ChatResponse) {
        if (selectedProvider != null && currentChatId == chat.id) {
            this.selectedProvider?.join()
            this.currentChatId = chat.id
            this.lastResponse = chat
            chatReceivedSubject.emit(chat)
            checkStatusSubject.emit(mapOf("chat" to chat, "check" to true))
            return
        }

        connect(chat)
    }

    /**
     * Enqueues the current chat session.
     */
    override suspend fun enqueueCurrentChat() {
        val chat = networkService.enqueue(
            currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId
        ).getOrThrow()
        lastResponse = chat
        chatReceivedSubject.emit(chat)
        checkStatus()
    }

    /**
     * Retrieves the last chat in progress, if any.
     *
     * @return The last in-progress chat response, or null if none.
     */
    override suspend fun getLastChatInProgress(): ChatResponse? {
        return networkService.getChatsFilterInProgress().getOrNull()?.lastOrNull()
    }

    /**
     * Sends a message in the current chat session.
     *
     * @param message The outgoing message content.
     */
    override suspend fun sendMessage(message: OutgoingMessageContent) {
        if (isTaskVaActive) {
            val taskVaMessage = sendTaskVaMessage(message)
            val messageId = taskVaMessage?.id ?: throw ChatNetworkError.InvalidTaskVaMessage
            selectedProvider?.sendMessage(TaskVaMessageRequest(type = ChatMessageBodyType.ServerMessage, messageId = messageId, visibility = "task_virtual_agent"))
            return
        }
        when (message) {
            is OutgoingMessageContent.Text -> {
                selectedProvider?.sendMessage(PlainTextChatMessageRequest(type = ChatMessageBodyType.Text, content = message.content))
            }

            is OutgoingMessageContent.Photos -> {
                val mediaIds = uploadPhotos(photos = message.photos, smartAction = message.smartAction, contentType = message.contentType)
                mediaIds.forEach { mediaId ->
                    selectedProvider?.sendMessage(MediaChatMessageRequest(type = ChatMessageBodyType.Photo, mediaId = mediaId))
                }
            }

            is OutgoingMessageContent.FormComplete -> {
                selectedProvider?.sendMessage(WebFormCompleteMessageRequest(type = ChatMessageBodyType.FormComplete, signature = message.signature, data = message.data))
            }
        }
    }

    /**
     * Sends a Task Virtual Agent message.
     *
     * @param message The outgoing message content.
     * @return The sent Task Virtual Agent message, or null if not applicable.
     */
    override suspend fun sendTaskVaMessage(message: OutgoingMessageContent): ChatTaskVaMessage? {
        val currentChatId = currentChatId ?: return null
        return when (message) {
            is OutgoingMessageContent.Text -> {
                networkService.sendTaskVaMessage(currentChatId, PlainTextChatMessageRequest(type = ChatMessageBodyType.Text, content = message.content)).getOrNull()
            }

            else -> {
                null
            }
        }
    }

    /**
     * Retrieves a Task Virtual Agent message by ID.
     *
     * @param messageId The message ID.
     * @return The Task Virtual Agent message, or null if not found.
     */
    override suspend fun getTaskVaMessage(messageId: Int?): ChatTaskVaMessage? {
        return networkService.getTaskVaMessage(currentChatId ?: return null, messageId).getOrNull()
    }

    /**
     * Sends a typing event to the provider.
     *
     * @param isTyping True if typing, false otherwise.
     */
    override fun typing(isTyping: Boolean) {
        selectedProvider?.typing(isTyping)
    }

    /**
     * Sends a message preview to the web while the user is typing.
     *
     * @param messagePreview The message preview string to send
     */
    override fun sendMessagePreview(messagePreview: String) {
        if (!isTaskVaActive) {
            selectedProvider?.sendMessagePreview(messagePreview)
        }
    }

    /**
     * Ends the current chat session.
     *
     * @return True if the chat was ended successfully, false otherwise.
     */
    override suspend fun endChat(): Boolean {
        selectedProvider?.leave()
        return networkService.finishChat(currentChatId ?: return false).isSuccess
    }

    /**
     * Escalates the current chat to a human agent.
     */
    override suspend fun escalateToHumanAgent() {
        networkService.escalateToHumanAgent(
            currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId
        )
        checkStatus()
    }

    /**
     * Checks the status of the current chat session.
     */
    override suspend fun checkStatus() {
        val chat = networkService.getChat(
            currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId
        ).getOrThrow()
        handleChatResponse(chat)
    }

    /**
     * Retrieves a web form URL for the given external form ID and smart action ID.
     *
     * @param externalFormId The external form ID.
     * @param smartActionId The smart action ID.
     * @return The web form response.
     */
    override suspend fun getFormUrl(externalFormId: String, smartActionId: Int): WebFormResponse {
        return webFormInterface?.handleWebFormRequest(
            WebFormRequest(
                externalFormId = externalFormId,
                smartActionId = smartActionId,
                signature = ""
            )
        ) ?: throw ChatNetworkError.InvalidWebFormInterface
    }

    /**
     * Validates the provided form data.
     *
     * @param formData The form data to verify.
     * @return True if the form data is valid, false otherwise.
     */
    override suspend fun verifyFormData(formData: WebFormResponse): Boolean {
        return networkService.verifyFormData(formData).isSuccess
    }

    /**
     * Handles a chat response and emits relevant events.
     *
     * @param chat The chat response to handle.
     */
    private suspend fun handleChatResponse(chat: ChatResponse) {
        if (lastResponse == null) return
        lastResponse = chat
        chatReceivedSubject.emit(chat)
        if (chat.status?.isCompleted() == true) {
            checkStatusSubject.emit(mapOf("chat" to chat, "check" to false))
        }
    }

    /**
     * Uploads a list of photos to the server for the current chat session.
     *
     * @param photos A list of photos represented as byte arrays to be uploaded.
     * @param smartAction An optional `SmartAction` object that provides additional context or metadata for the upload.
     * @param contentType An optional `contentType` String that provides photo mimeType for the upload.
     * @return A list of integers representing the IDs of the uploaded photos.
     * @throws ChatNetworkError.InvalidCurrentChatId If the current chat ID is null or invalid.
     */
    override suspend fun uploadPhotos(photos: List<ByteArray>, smartAction: SmartAction?, contentType: String?): List<Int> {
        val chatId = currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId
        return withContext(Dispatchers.IO) {
            networkService.uploadPhotos(chatId, photos, "photo", smartAction, contentType).getOrThrow()
        }
    }

    /**
     * Escalates the current chat with a deflection channel.
     *
     * @param escalationId The escalation ID.
     * @param deflectionChannel The deflection channel.
     */
    override suspend fun escalate(escalationId: Int, deflectionChannel: String) {
        networkService.escalateChatWithDeflection(
            currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId,
            escalationId,
            deflectionChannel
        )
    }

    /**
     * Sends a chat event to the server.
     *
     * @param event The chat event to send.
     */
    override suspend fun sendEvent(event: ChatEvent) {
        networkService.postChatEvent(currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId, event)
    }

    /**
     * Retrieves custom form details for the given form ID.
     *
     * @param formId The form ID.
     * @return The custom form details response, or null if not found.
     */
    override suspend fun getCustomFormDetails(formId: Int): CustomFormDetailsResponse? {
        return networkService.getCustomFormDetails(formId).getOrNull()
    }

    /**
     * Submits a custom form request.
     *
     * @param request The custom form request.
     * @return True if the form was submitted successfully, false otherwise.
     */
    override suspend fun submitCustomForm(request: SubmitCustomFormRequest): Boolean {
        return networkService.submitCustomForm(request).isSuccess
    }

    /**
     * Retrieves previous chat history with pagination.
     *
     * @param page The page number.
     * @param perPage The number of items per page.
     * @return The chat history info, or null if not available.
     */
    override suspend fun getPreviousChats(page: Int, perPage: Int): ChatHistoryInfo? {
        val endUserId = CCAI.getEndUser()?.id ?: run {
            LoggingUtil.log("End user ID is null", LogLevel.DEBUG)
            return null
        }
        val historyResponse = networkService.getHistoryMessages(page, perPage).getOrThrow()

        val hasChats = historyResponse.chats?.isNotEmpty() == true
        val nextPage = historyResponse.pagination?.nextPage?.takeIf { hasChats }

        val messages = mutableListOf<ChatMessage>()
        val agents = mutableListOf<Agent>()
        var containsServerMessage = false

        for (chat in historyResponse.chats.orEmpty()) {
            val currentMessages = chat.getPreviousChatMessages(endUserId)
            messages += currentMessages
            agents += chat.getChatAgents()

            if (!containsServerMessage && currentMessages.any { it.body.type == ChatMessageBodyType.ServerMessage }) {
                containsServerMessage = true
            }
        }
        messages.sortBy { it.date }

        if (containsServerMessage) {
            val taskVaMessages = getTaskVaMessages().orEmpty().associateBy { it.id }

            messages.forEach { message ->
                if (message.body.type == ChatMessageBodyType.ServerMessage) {
                    taskVaMessages[message.body.messageId]?.let { taskVaMessage ->
                        message.body = taskVaMessage.body
                        message.date = taskVaMessage.date
                    }
                }
            }
        }

        return ChatHistoryInfo(
            chatMessages = checkFormCompletedStatus(messages),
            agents = agents,
            nextPage = nextPage,
        )
    }

    /**
     * Retrieves Task Virtual Agent messages for the current chat.
     *
     * @return A list of Task Virtual Agent chat messages, or null if not available.
     */
    override suspend fun getTaskVaMessages(): List<ChatMessage>? {
        return networkService.getTaskVaMessages(currentChatId ?: return null).getOrThrow()
            .map { it.convertToChatMessage() }
    }

    /**
     * Checks if forms are completed and updates their status in the message list.
     * Removes form completed messages from history.
     *
     * @param messages The list of chat messages.
     * @return The filtered list of chat messages.
     */
    private fun checkFormCompletedStatus(messages: List<ChatMessage>): List<ChatMessage> {
        val completedWebFormIds = messages
            .filter { it.body.type == ChatMessageBodyType.FormComplete && it.body.data?.status == "success" }
            .map { it.body.data?.smartActionId ?: 0 }
            .filter { it > 0 }
        val completedCustomFormIds = messages
            .filter { it.body.type == ChatMessageBodyType.FormComplete }
            .map { it.body.data?.smartActionId ?: 0 }
            .filter { it > 0 }

        messages.forEach { message ->
            val form = message.body.form ?: return@forEach
            val id = form.smartActionId
            form.completed = when (form) {
                is Form.WebForm -> id in completedWebFormIds
                is Form.CustomForm -> id in completedCustomFormIds
            }
        }
        return messages.filterNot { it.body.type == ChatMessageBodyType.FormComplete }
    }

    /**
     * Downloads the PDF transcript for the specified chat transcript ID.
     * @return The downloaded PDF file, or null if download fails.
     */
    override suspend fun downloadChatTranscript(): File? {
        val transcriptId = networkService.generateChatTranscript(currentChatId ?: throw ChatNetworkError.InvalidCurrentChatId).getOrNull()?.chatTranscriptId
        if (transcriptId == null) {
            return null
        }

        return downloadChatTranscript(transcriptId, CHAT_TRANSCRIPT_MAX_RETRY_COUNT, CHAT_TRANSCRIPT_DELAY_INTERVAL)
    }

    private suspend fun downloadChatTranscript(transcriptId: Int, maxRetries: Int, delay: Long): File? {
        var file: File? = null
        try {
            file = networkService.downloadChatTranscript(transcriptId).getOrNull()
        } catch (e: Exception) {
            LoggingUtil.log("Error downloading chat transcript: ${e.message}", LogLevel.ERROR)
        }
        if (file == null && maxRetries > 0) {
            delay(delay)
            file = downloadChatTranscript(transcriptId, maxRetries - 1, delay)
        }

        return file
    }
}
