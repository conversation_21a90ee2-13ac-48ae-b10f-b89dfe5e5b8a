package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the type of a button in a content card component.
 *
 * @property value The string value associated with the button type.
 */
@JsonAdapter(CardButtonTypeAdapter::class)
enum class CardButtonType(val value: String) {
    /** Primary button type. */
    Primary("primary"),

    /** Secondary button type. */
    Secondary("secondary"),

    /** No button type. */
    None("");

    companion object {
        /**
         * Returns the [CardButtonType] corresponding to the given string value.
         *
         * @param value The string value of the button type.
         * @return The matching [CardButtonType], or [None] if not found.
         */
        fun fromValue(value: String): CardButtonType {
            return entries.find { it.value == value } ?: None
        }
    }
}

/**
 * Gson type adapter for serializing and deserializing [CardButtonType].
 *
 * This adapter converts between the enum's string value and the enum instance
 * when reading from or writing to JSON.
 */
class CardButtonTypeAdapter : JsonSerializer<CardButtonType>, JsonDeserializer<CardButtonType> {

    override fun serialize(
        src: CardButtonType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.value ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): CardButtonType {
        return CardButtonType.fromValue(json?.asString ?: "")
    }
}
