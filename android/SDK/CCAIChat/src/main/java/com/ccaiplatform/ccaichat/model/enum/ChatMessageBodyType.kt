package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the type of body content in a chat message.
 *
 * @property value The string value associated with the body type.
 */
@JsonAdapter(ChatMessageBodyTypeAdapter::class)
enum class ChatMessageBodyType(val value: String) {
    /** Unknown body type. */
    Unknown("unknown"),

    /** Plain text message. */
    Text("text"),

    /** Photo message. */
    Photo("photo"),

    /** Video message. */
    Video("video"),

    /** Notification message. */
    Notification("noti"),

    /** Text template message. */
    TextTemplate("text_template"),

    /** Markdown message. */
    Markdown("markdown"),

    /** Markdown template message. */
    MarkdownTemplate("markdown_template"),

    /** Inline button message. */
    InlineButton("inline_button"),

    /** Sticky button message. */
    StickyButton("sticky_button"),

    /** Document message. */
    Document("document"),

    /** Image message. */
    Image("image"),

    /** Content card message. */
    ContentCard("content_card"),

    /** Form message. */
    Form("form"),

    /** Form completion message. */
    FormComplete("form_complete"),

    /** Server-generated message. */
    ServerMessage("server_message");

    companion object {
        /**
         * Returns the [ChatMessageBodyType] corresponding to the given string value.
         *
         * @param value The string value of the body type.
         * @return The matching [ChatMessageBodyType], or [Unknown] if not found.
         */
        fun fromValue(value: String): ChatMessageBodyType {
            return entries.find { it.value == value } ?: Unknown
        }
    }
}

/**
 * Gson type adapter for serializing and deserializing [ChatMessageBodyType].
 *
 * This adapter converts between the enum's string value and the enum instance
 * when reading from or writing to JSON.
 */
class ChatMessageBodyTypeAdapter : JsonSerializer<ChatMessageBodyType>, JsonDeserializer<ChatMessageBodyType> {

    override fun serialize(
        src: ChatMessageBodyType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.value ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ChatMessageBodyType {
        return ChatMessageBodyType.fromValue(json?.asString ?: "")
    }
}
