package com.ccaiplatform.ccaichat.model

import java.io.ByteArrayOutputStream

class HttpFormData {
    private val boundary = "===" + System.currentTimeMillis() + "==="
    private val lineBreak = "\r\n"
    private val data = ByteArrayOutputStream()
    private var finalizedData: ByteArray? = null

    val contentType: String
        get() = "multipart/form-data; boundary=$boundary"

    val dataRepresentation: ByteArray
        get() {
            if (finalizedData == null) {
                val end = "--$boundary--$lineBreak"
                data.write(end.toByteArray(Charsets.UTF_8))
                finalizedData = data.toByteArray()
            }
            return finalizedData!!
        }

    fun append(params: Map<String, String>) {
        for ((key, value) in params) {
            append(key, value)
        }
    }

    fun append(name: String, value: String) {
        if (value.isEmpty()) return
        writeBoundary()
        writeDisposition(name)
        writeContent(value)
    }

    fun append(name: String, fileName: String, contentType: String?, fileData: ByteArray) {
        writeBoundary()
        writeDisposition(name, fileName)
        writeContentType(contentType)
        writeContent(fileData)
    }

    private fun writeBoundary() {
        val boundaryLine = "--$boundary$lineBreak"
        data.write(boundaryLine.toByteArray(Charsets.UTF_8))
    }

    private fun writeDisposition(name: String, fileName: String? = null) {
        val disposition = if (fileName == null) {
            "Content-Disposition: form-data; name=\"$name\"$lineBreak$lineBreak"
        } else {
            "Content-Disposition: form-data; name=\"$name\"; filename=\"$fileName\"$lineBreak"
        }
        data.write(disposition.toByteArray(Charsets.UTF_8))
    }

    private fun writeContentType(contentType: String?) {
        if (contentType != null) {
            val typeLine = "Content-Type: $contentType$lineBreak$lineBreak"
            data.write(typeLine.toByteArray(Charsets.UTF_8))
        } else {
            data.write(lineBreak.toByteArray(Charsets.UTF_8))
        }
    }

    private fun writeContent(content: String) {
        val contentLine = "$content$lineBreak"
        data.write(contentLine.toByteArray(Charsets.UTF_8))
    }

    private fun writeContent(content: ByteArray) {
        data.write(content)
        data.write(lineBreak.toByteArray(Charsets.UTF_8))
    }

}
