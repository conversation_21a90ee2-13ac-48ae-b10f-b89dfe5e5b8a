package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents errors that can occur in the chat service layer.
 */
sealed class ChatServiceError : Error() {
    /**
     * Indicates a failure to get the chat provider.
     */
    object FailedToGetProvider : ChatServiceError() {
        private fun readResolve(): Any = FailedToGetProvider
    }

    /**
     * Indicates a failure to get the chat.
     */
    object FailedToGetChat : ChatServiceError() {
        private fun readResolve(): Any = FailedToGetChat
    }
}
