package com.ccaiplatform.ccaichat.interfaces

import com.ccaiplatform.ccaichat.model.ChatMessageRequest

/**
 * Interface for chat provider implementations, defining core chat operations.
 */
interface ChatProviderInterface {
    /**
     * Joins the chat session.
     */
    suspend fun join()

    /**
     * Leaves the chat session.
     */
    suspend fun leave()

    /**
     * Sets a listener to receive chat events.
     *
     * @param listener The listener to receive chat events, or null to remove.
     */
    fun setListener(listener: ChatProviderListener?)

    /**
     * Sends a chat message.
     *
     * @param message The message request to send.
     */
    suspend fun sendMessage(message: ChatMessageRequest)

    /**
     * Sends a message preview while the user is typing.
     *
     * @param messagePreview The preview text to send.
     */
    fun sendMessagePreview(messagePreview: String)

    /**
     * Notifies the chat provider of typing state.
     *
     * @param isTyping True if the user is typing, false otherwise.
     */
    fun typing(isTyping: Boolean)
}
