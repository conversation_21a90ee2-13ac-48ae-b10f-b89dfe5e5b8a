package com.ccaiplatform.ccaichat.model.enum

import android.net.Uri
import com.ccaiplatform.ccaichat.model.FormCompleteEventData
import com.ccaiplatform.ccaikit.models.SmartAction

/**
 * Represents the content of an outgoing message in the chat.
 */
sealed class OutgoingMessageContent {
    /**
     * Text message content.
     *
     * @property content The text content of the message.
     */
    data class Text(val content: String) : OutgoingMessageContent()

    /**
     * Photo message content, including photo data and URIs.
     *
     * @property photos The list of photo byte arrays.
     * @property uris The list of photo URIs.
     * @property smartAction Optional smart action associated with the photos.
     * @property contentType Optional content type of the photos.
     */
    data class Photos(
        val photos: List<ByteArray>,
        val uris: List<Uri>,
        val smartAction: SmartAction? = null,
        val contentType: String? = null
    ) : OutgoingMessageContent()

    /**
     * Form completion message content.
     *
     * @property type Optional type of the form.
     * @property signature Optional signature for the form.
     * @property data The form completion event data.
     */
    data class FormComplete(
        val type: String? = null,
        val signature: String? = null,
        val data: FormCompleteEventData
    ) : OutgoingMessageContent()
}
