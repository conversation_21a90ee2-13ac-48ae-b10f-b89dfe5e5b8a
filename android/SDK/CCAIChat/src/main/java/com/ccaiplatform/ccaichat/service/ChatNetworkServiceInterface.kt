package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatHistoryResponse
import com.ccaiplatform.ccaichat.model.ChatMessageRequest
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.ChatTaskVaMessage
import com.ccaiplatform.ccaikit.models.response.transcript.GenerateChatTranscriptResponse
import com.ccaiplatform.ccaichat.model.CustomFormDetailsResponse
import com.ccaiplatform.ccaichat.model.SubmitCustomFormRequest
import com.ccaiplatform.ccaichat.model.WebFormResponse
import com.ccaiplatform.ccaikit.models.SmartAction
import java.io.File

/**
 * Interface defining network operations for chat-related features.
 * Provides methods for chat creation, message handling, form processing, and escalation.
 */
interface ChatNetworkServiceInterface {
    /**
     * Creates a new chat session for the given menu ID.
     *
     * @param menuId The menu identifier.
     * @return A [Result] containing the created [ChatResponse].
     */
    suspend fun createChat(menuId: Int): Result<ChatResponse>

    /**
     * Retrieves a chat provider token for authentication.
     *
     * @param chatId The ID of the chat session.
     * @param providerType The type of chat provider (e.g. "twilio_conversations").
     * @return A [Result] containing the authentication token for the chat provider.
     */
    suspend fun getToken(chatId: Int, providerType: String): Result<String>

    /**
     * Fetches chat details by ID.
     *
     * @param id The ID of the chat session to retrieve.
     * @return A [Result] containing the [ChatResponse].
     */
    suspend fun getChat(id: Int): Result<ChatResponse>

    /**
     * Validates a web form response in a chat session.
     *
     * @param formData The form data to verify.
     * @return A [Result] indicating whether the form is valid.
     */
    suspend fun verifyFormData(formData: WebFormResponse): Result<Unit>

    /**
     * Marks the chat as finished.
     *
     * @param id The ID of the chat session.
     * @return A [Result] indicating success or failure.
     */
    suspend fun finishChat(id: Int): Result<Unit>

    /**
     * Enqueues the chat for processing.
     *
     * @param id The ID of the chat session.
     * @return A [Result] containing the updated [ChatResponse].
     */
    suspend fun enqueue(id: Int): Result<ChatResponse>

    /**
     * Retrieves a task VA message for the given chat and message ID.
     *
     * @param chatId The ID of the chat session.
     * @param messageId The message identifier, or null.
     * @return A [Result] containing the [ChatTaskVaMessage].
     */
    suspend fun getTaskVaMessage(chatId: Int, messageId: Int?): Result<ChatTaskVaMessage>

    /**
     * Sends a task VA message for the specified chat.
     *
     * @param chatId The ID of the chat session.
     * @param message The message request to send.
     * @return A [Result] containing the [ChatTaskVaMessage].
     */
    suspend fun sendTaskVaMessage(chatId: Int, message: ChatMessageRequest): Result<ChatTaskVaMessage>

    /**
     * Retrieves a list of chats filtered by in-progress status.
     *
     * @return A [Result] containing a list of [ChatResponse] representing active chat sessions.
     */
    suspend fun getChatsFilterInProgress(): Result<List<ChatResponse>>

    /**
     * Escalates a chat session to a human agent.
     *
     * @param chatId The ID of the chat session.
     */
    suspend fun escalateToHumanAgent(chatId: Int)

    /**
     * Escalates the chat with deflection information.
     *
     * @param chatId The ID of the chat session.
     * @param escalationId The escalation identifier.
     * @param deflectionChannel The deflection channel.
     */
    suspend fun escalateChatWithDeflection(chatId: Int, escalationId: Int, deflectionChannel: String)

    /**
     * Posts a chat event to a chat session.
     *
     * @param chatId The ID of the chat session.
     * @param event The chat event to post.
     */
    suspend fun postChatEvent(chatId: Int, event: ChatEvent)

    /**
     * Uploads photos to the chat.
     *
     * @param chatId The ID of the chat session.
     * @param photos The list of photo byte arrays.
     * @param type The type of media being uploaded. (e.g. "photo" or "screenshot").
     * @param smartAction Optional smart action associated with the upload.
     * @param contentType Optional contentType of photo mimeType with the upload.
     * @return A [Result] containing a list of uploaded photo IDs.
     */
    suspend fun uploadPhotos(chatId: Int, photos: List<ByteArray>, type: String,
                             smartAction: SmartAction?,contentType: String?): Result<List<Int>>

    /**
     * Retrieves chat history messages. This method retrieves messages from the chat history,
     * which may include messages from previous sessions and it is useful for displaying the
     * full conversation history to the user.
     *
     * @param page The page number.
     * @param perPage The number of messages per page.
     * @return A [Result] containing the [ChatHistoryResponse].
     */
    suspend fun getHistoryMessages(page: Int, perPage: Int): Result<ChatHistoryResponse>

    /**
     * Retrieves all task VA messages for the specified chat.
     *
     * @param chatId The ID of the chat session.
     * @return A [Result] containing a list of [ChatTaskVaMessage].
     */
    suspend fun getTaskVaMessages(chatId: Int): Result<List<ChatTaskVaMessage>>

    /**
     * Retrieves custom form details by form ID.
     *
     * @param formId The form identifier.
     * @return A [Result] containing the [CustomFormDetailsResponse].
     */
    suspend fun getCustomFormDetails(formId: Int): Result<CustomFormDetailsResponse>

    /**
     * Submits a custom form.
     *
     * @param request The form submission request.
     * @return A [Result] containing the submission result.
     */
    suspend fun submitCustomForm(request: SubmitCustomFormRequest): Result<Any>

    /**
     * Generates a PDF transcript file for the chat.
     *
     * @param chatId The ID of the chat session.
     * @return A [Result] containing the [GenerateChatTranscriptResponse].
     */
    suspend fun generateChatTranscript(chatId: Int): Result<GenerateChatTranscriptResponse>

    /**
     * Downloads the chat PDF transcript file.
     *
     * @param chatTranscriptId The ID of the chat transcript file.
     * @return A [Result] containing the downloaded file.
     */
    suspend fun downloadChatTranscript(chatTranscriptId: Int): Result<File>
}
