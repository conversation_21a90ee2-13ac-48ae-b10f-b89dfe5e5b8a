package com.ccaiplatform.ccaichat.interfaces

import android.content.Context
import com.ccaiplatform.ccaichat.model.ChatOptions
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.service.ChatNetworkServiceInterface
import com.ccaiplatform.ccaikit.interfaces.ProviderModuleInterface

/**
 * Interface for defining requirements for chat provider modules. Chat provider modules create
 * provider-specific chat implementations based on server responses.
 * Extends [ProviderModuleInterface] to supply a chat provider instance.
 */
interface ChatProviderModuleInterface : ProviderModuleInterface {
    /**
     * Creates a chat provider instance.
     *
     * @param context The Android context.
     * @param chatResponse The server response containing chat session details.
     * @param options Optional chat options.
     * @param networkService The network service interface for chat operations.
     * @return An instance of [ChatProviderInterface] that implements the specified chat functionality.
     */
    fun chatProvider(
        context: Context,
        chatResponse: ChatResponse,
        options: ChatOptions?,
        networkService: ChatNetworkServiceInterface
    ): ChatProviderInterface
}
