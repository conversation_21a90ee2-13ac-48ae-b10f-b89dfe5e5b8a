package com.ccaiplatform.ccaichat.service

import androidx.core.net.toUri
import com.ccaiplatform.ccaichat.model.ChatAPIEndpoint
import com.ccaiplatform.ccaichat.model.HttpFormData
import com.ccaiplatform.ccaichat.model.PhotoPathRequest
import com.ccaiplatform.ccaichat.model.PhotoRequest
import com.ccaiplatform.ccaichat.model.PhotoResponse
import com.ccaiplatform.ccaichat.model.SignedURLResponse
import com.ccaiplatform.ccaichat.model.enum.MediaType
import com.ccaiplatform.ccaichat.model.enum.MediaUploadError
import com.ccaiplatform.ccaichat.model.enum.MimeType
import com.ccaiplatform.ccaichat.util.TempFileUtil
import com.ccaiplatform.ccaikit.models.SmartAction
import com.ccaiplatform.ccaikit.services.network.HttpMethod
import com.ccaiplatform.ccaikit.services.network.NetworkRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import java.io.File

suspend fun ChatNetworkService.uploadPhotosInternal(chatId: Int, photos: List<ByteArray>, mediaType: String, smartAction: SmartAction?, contentType: String?): Result<List<Int>> = runCatching {
    if (photos.isEmpty()) throw MediaUploadError.UploadError()
    val photoUploads = coroutineScope {
        val tasks = photos.map { imageData ->
            async { getUploadPhotoTask(imageData, chatId, mediaType, contentType) }
        }
        tasks.awaitAll().associateBy {
            it.s3Path
        }
    }
    val photoRequest = PhotoRequest(
        photo = photoUploads.values.toList(),
        photoType = MediaType.Photo,
        multiple = true,
        smartActionId = smartAction?.id
    )
    val apiRequest = NetworkRequest(
        method = HttpMethod.POST,
        path = ChatAPIEndpoint.uploadPhotos(chatId),
        body = photoRequest
    )
    val photoResults: List<PhotoResponse> = networkService.sendRequest(apiRequest)
    photoResults.forEach {
        val imageData = photos.getOrNull(photoUploads.keys.indexOf(it.s3Path)) ?: return@forEach
        val path = TempFileUtil.getUploadedFilePathForPhoto(networkService.context, chatId, it.mediaId) ?: return@forEach
        withContext(Dispatchers.IO) {
            runCatching { File(path).writeBytes(imageData) }
        }
    }
    photoResults.map { it.mediaId }
}

suspend fun ChatNetworkService.getUploadPhotoTask(imageData: ByteArray, chatId: Int, mediaType: String, contentType: String?): PhotoPathRequest {
    val signedUrl = requestPreSignedURL(chatId).getOrThrow()
    val s3url = signedUrl.url
    val options = signedUrl.fields
    val key = options["key"] ?: throw MediaUploadError.UploadError()
    val keyUri = key.toUri()
    val fileName = keyUri.lastPathSegment ?: ""
    val formData = HttpFormData().apply {
        append(options)
        append("file", fileName, contentType ?: MimeType.JPEG.type, imageData)
    }
    postFormData(s3url, formData)
    return PhotoPathRequest(
        s3Path = key,
        photoType = MediaType.fromValue(mediaType) ?: MediaType.Photo,
    )
}

suspend fun ChatNetworkService.requestPreSignedURL(chatId: Int): Result<SignedURLResponse> {
    val apiRequest = NetworkRequest(
        method = HttpMethod.POST,
        path = ChatAPIEndpoint.uploadPhotosPreSignedUrl(chatId),
    )
    return runCatching {
        networkService.sendRequest<SignedURLResponse>(apiRequest)
    }
}

private fun ChatNetworkService.postFormData(path: String, data: HttpFormData) {
    return networkService.sendExternalRequest(
        method = HttpMethod.POST,
        url = path,
        body = data.dataRepresentation,
        headerFields = hashMapOf("Content-Type" to data.contentType)
    )
}
