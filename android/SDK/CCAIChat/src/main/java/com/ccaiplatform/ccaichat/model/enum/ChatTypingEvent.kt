package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents typing events in a chat, such as when a user starts or ends typing.
 */
sealed class ChatTypingEvent {
    /**
     * Indicates that typing has started.
     *
     * @param identity The identifier of the user who started typing, or null if unknown.
     */
    data class Started(val identity: String?) : ChatTypingEvent()

    /**
     * Indicates that typing has ended.
     *
     * @param identity The identifier of the user who ended typing, or null if unknown.
     */
    data class Ended(val identity: String?) : ChatTypingEvent()
}
