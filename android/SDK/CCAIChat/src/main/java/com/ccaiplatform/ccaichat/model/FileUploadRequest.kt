package com.ccaiplatform.ccaichat.model

import com.google.gson.annotations.SerializedName
import com.ccaiplatform.ccaichat.model.enum.MediaType

data class PhotoRequest(
    @SerializedName("photo")
    val photo: List<PhotoPathRequest>,

    @SerializedName("photo_type")
    val photoType: MediaType,

    @SerializedName("multiple")
    val multiple: <PERSON><PERSON><PERSON>,

    @SerializedName("smart_action_id")
    val smartActionId: Int?
)

data class PhotoPathRequest(
    @SerializedName("s3_path")
    val s3Path: String,

    @SerializedName("photo_type")
    val photoType: MediaType,
)

data class PhotoResponse(
    @SerializedName("url")
    val url: String,

    @SerializedName("media_id")
    val mediaId: Int,

    @SerializedName("s3_path")
    val s3Path: String
)

data class SignedURLResponse(
    @SerializedName("url")
    val url: String,

    @SerializedName("fields")
    val fields: Map<String, String>
)
