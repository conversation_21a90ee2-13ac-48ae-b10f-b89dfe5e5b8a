package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents events related to the transfer of a chat session.
 * This sealed class is used to model the different states or outcomes
 * of a chat transfer process.
 */
sealed class ChatTransferEvent {
    /**
     * Indicates that the chat transfer process has started.
     *
     * @property identity An optional identifier for the entity initiating the transfer.
     */
    data class Started(val identity: String?) : ChatTransferEvent()

    /**
     * Indicates that the chat transfer process has been accepted.
     *
     * @property identity An optional identifier for the entity accepting the transfer.
     */
    data class Accepted(val identity: String?) : ChatTransferEvent()

    /**
     * Indicates that the chat transfer process has failed.
     *
     * @property identity An optional identifier for the entity involved in the failed transfer.
     */
    data class Failed(val identity: String?) : ChatTransferEvent()
}
