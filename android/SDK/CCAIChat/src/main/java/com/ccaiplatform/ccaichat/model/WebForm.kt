package com.ccaiplatform.ccaichat.model

import com.google.gson.annotations.SerializedName

data class WebFormResponse(
    @SerializedName("type")
    val type: String? = null,

    @SerializedName("signature")
    val signature: String? = null,

    @SerializedName("data")
    val data: WebFormData? = null,
)

data class WebFormData(
    @SerializedName("external_form_id")
    val externalFormId: String? = null,

    @SerializedName("smart_action_id")
    val smartActionId: Int = 0,

    @SerializedName("uri")
    val uri: String? = null,
)

data class WebFormRequest(
    @SerializedName("signature")
    val signature: String,

    @SerializedName("smart_action_id")
    val smartActionId: Int,

    @SerializedName("external_form_id")
    val externalFormId: String,

    @SerializedName("type")
    val type: String = "form_message_received"
)
