package com.ccaiplatform.ccaichat.model.enum

import android.os.Parcelable
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Type

/**
 * Represents the status of a chat session.
 *
 * @property rawValue The string value representing the status.
 */
@Parcelize
@JsonAdapter(ChatStatusTypeAdapter::class)
sealed class ChatStatus(val rawValue: String): Parcelable {

    /** Chat is queued and waiting to be assigned. */
    object Queued : ChatStatus("queued")

    /** Chat is assigned to an agent or virtual agent. */
    object Assigned : ChatStatus("assigned")

    /** Chat has been dismissed. */
    object Dismissed : ChatStatus("dismissed")

    /** Chat has been finished. */
    object Finished : ChatStatus("finished")

    /** Chat has been canceled. */
    object Canceled : ChatStatus("canceled")

    /** Chat has failed. */
    object Failed : ChatStatus("failed")

    /** Chat is assigned to a virtual agent. */
    object VaAssigned : ChatStatus("va_assigned")

    /** Chat has been dismissed by a virtual agent. */
    object VaDismissed : ChatStatus("va_dismissed")

    /**
     * Represents an unknown or custom chat status.
     *
     * @property value The unknown status value.
     */
    class Unknown(val value: String = "") : ChatStatus(value)

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is ChatStatus) return false

        return when {
            this is Unknown && other is Unknown -> this.rawValue == other.rawValue
            else -> this.rawValue == other.rawValue
        }
    }

    override fun hashCode(): Int {
        return rawValue.hashCode()
    }

    companion object {
        /**
         * List of all valid chat statuses.
         */
        val validStatus by lazy {
            listOf(
                Queued, Assigned, Dismissed, Finished, Canceled, Failed, VaAssigned, VaDismissed
            )
        }

        /**
         * List of statuses considered as in-progress.
         */
        val inProgressStatus by lazy {
            listOf(
                Queued, Assigned, VaAssigned, Dismissed, VaDismissed
            )
        }

        /**
         * List of statuses considered as dismissed.
         */
        private val dismissedStatus by lazy {
            listOf(
                Dismissed, VaDismissed
            )
        }

        /**
         * List of statuses considered as completed.
         */
        private val completedStatus by lazy {
            listOf(
                Finished, Canceled, Failed
            )
        }

        /**
         * Returns a [ChatStatus] from its raw string value.
         *
         * @param value The raw status string.
         * @return The corresponding [ChatStatus] or [Unknown] if not found.
         */
        fun fromRawValue(value: String?): ChatStatus {
            if (value.isNullOrEmpty()) return Unknown("invalid_empty")
            return validStatus.firstOrNull { it.rawValue == value } ?: Unknown(value)
        }
    }

    /**
     * Checks if the status is considered in-progress.
     *
     * @return `true` if in-progress, `false` otherwise.
     */
    fun isInProgress(): Boolean {
        return inProgressStatus.firstOrNull { it.rawValue == this.rawValue } != null
    }

    /**
     * Checks if the status is considered completed.
     *
     * @return `true` if completed, `false` otherwise.
     */
    fun isCompleted(): Boolean {
        return completedStatus.firstOrNull { it.rawValue == this.rawValue } != null
    }

    /**
     * Checks if the status is considered dismissed.
     *
     * @return `true` if dismissed, `false` otherwise.
     */
    fun isDismissed(): Boolean {
        return dismissedStatus.firstOrNull { it.rawValue == this.rawValue } != null
    }
}

/**
 * Gson type adapter for serializing and deserializing [ChatStatus].
 */
class ChatStatusTypeAdapter : JsonSerializer<ChatStatus>, JsonDeserializer<ChatStatus> {

    override fun serialize(
        src: ChatStatus?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.rawValue ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ChatStatus {
        return ChatStatus.fromRawValue(json?.asString ?: "")
    }
}
