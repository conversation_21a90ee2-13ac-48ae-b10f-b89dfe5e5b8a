package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the various events that can occur in a chat message lifecycle.
 *
 * @property value The string value associated with the event.
 */
@JsonAdapter(ChatMessageEventAdapter::class)
enum class ChatMessageEvent(val value: String) {
    /** No event. */
    None(""),

    /** Photo was requested from the user. */
    PhotoRequested("photoRequested"),

    /** Video was requested from the user. */
    VideoRequested("videoRequested"),

    /** Screenshot was requested from the user. */
    ScreenshotRequested("screenshotRequested"),

    /** Verification was requested from the user. */
    VerificationRequested("verificationRequested"),

    /** End user has been verified. */
    EndUserVerified("endUserVerified"),

    /** End user sent a photo. */
    EndUserSentPhoto("endUserSentPhoto"),

    /** End user sent a video. */
    EndUserSentVideo("endUserSentVideo"),

    /** A member left the chat. */
    MemberLeft("memberLeft"),

    /** The chat has ended. */
    ChatEnded("chatEnded"),

    /** The chat was dismissed. */
    ChatDismissed("chatDismissed"),

    /** An agent joined the chat. */
    AgentJoined("agentJoined"),

    /** A transfer has started. */
    TransferStarted("transferStarted"),

    /** A transfer was accepted. */
    TransferAccepted("transferAccepted"),

    /** A transfer failed. */
    TransferFailed("transferFailed"),

    /** Escalation process started. */
    EscalationStarted("escalationStarted"),

    /** Escalation was accepted. */
    EscalationAccepted("escalationAccepted"),

    /** Escalation failed. */
    EscalationFailed("escalationFailed"),

    /** Escalation was deflected. */
    EscalationDeflected("escalationDeflected"),

    /** Cobrowse was requested by the agent. */
    CobrowseRequestedFromAgent("cobrowseRequestedFromAgent"),

    /** Cobrowse was requested by the end user. */
    CobrowseRequestedFromEndUser("cobrowseRequestedFromEndUser"),

    /** Cobrowse code was generated. */
    CobrowseCodeGenerated("cobrowseCodeGenerated"),

    /** Cobrowse failed. */
    CobrowseFailed("cobrowseFailed"),

    /** Cobrowse session started. */
    CobrowseStarted("cobrowseStarted"),

    /** Cobrowse session ended. */
    CobrowseEnded("cobrowseEnded"),

    /** Custom event. */
    Custom("custom"),

    /** Task virtual agent started. */
    TaskVaStarted("taskVaStarted"),

    /** Task virtual agent finished. */
    TaskVaFinished("taskVaFinished"),

    /** Form was requested. */
    FormRequested("formRequested"),

    /** Form was completed. */
    FormCompleted("formCompleted");

    companion object {
        /**
         * Returns the [ChatMessageEvent] corresponding to the given string value.
         *
         * @param value The string value of the event.
         * @return The matching [ChatMessageEvent], or [None] if not found.
         */
        fun fromValue(value: String): ChatMessageEvent {
            return entries.find { it.value == value } ?: None
        }
    }
}

/**
 * Gson type adapter for serializing and deserializing [ChatMessageEvent].
 */
class ChatMessageEventAdapter : JsonSerializer<ChatMessageEvent>, JsonDeserializer<ChatMessageEvent> {

    override fun serialize(
        src: ChatMessageEvent?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.value ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): ChatMessageEvent {
        return ChatMessageEvent.fromValue(json?.asString ?: "")
    }
}
