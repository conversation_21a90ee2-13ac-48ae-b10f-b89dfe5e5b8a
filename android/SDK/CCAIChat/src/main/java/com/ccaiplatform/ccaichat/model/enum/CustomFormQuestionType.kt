package com.ccaiplatform.ccaichat.model.enum

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.JsonAdapter
import java.lang.reflect.Type

/**
 * Represents the type of a custom form question.
 *
 * @property value The string value associated with the question type.
 */
@JsonAdapter(CustomFormQuestionTypeAdapter::class)
enum class CustomFormQuestionType(val value: String) {
    /** Text entry question type. */
    TextEntry("text_entry"),

    /** List picker question type. */
    ListPicker("list_picker"),

    /** Date question type. */
    Date("date"),

    /** Time question type. */
    Time("time"),

    /** Toggle (boolean) question type. */
    Toggle("toggle"),

    /** No question type. */
    None("");

    companion object {
        /**
         * Returns the [CustomFormQuestionType] corresponding to the given string value.
         *
         * @param value The string value of the question type.
         * @return The matching [CustomFormQuestionType], or [None] if not found.
         */
        fun fromValue(value: String): CustomFormQuestionType {
            return CustomFormQuestionType.entries.find { it.value == value } ?: None
        }
    }
}

/**
 * Gson type adapter for serializing and deserializing [CustomFormQuestionType].
 *
 * This adapter converts between the enum's string value and the enum instance
 * when reading from or writing to JSON.
 */
class CustomFormQuestionTypeAdapter : JsonSerializer<CustomFormQuestionType>, JsonDeserializer<CustomFormQuestionType> {
    override fun serialize(
        src: CustomFormQuestionType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.value ?: "")
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): CustomFormQuestionType {
        return CustomFormQuestionType.fromValue(json?.asString ?: "")
    }
}
