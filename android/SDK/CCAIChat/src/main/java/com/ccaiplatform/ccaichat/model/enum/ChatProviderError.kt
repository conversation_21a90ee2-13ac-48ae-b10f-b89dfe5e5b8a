package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents errors that can occur when interacting with a chat provider.
 */
sealed class ChatProviderError : Exception() {

    /**
     * Indicates that the provided token is invalid.
     */
    data object InvalidToken : ChatProviderError() {
        private fun readResolve(): Any = InvalidToken
    }

    /**
     * Indicates a failure to initialize the chat provider.
     */
    data object FailedToInitialize : ChatProviderError() {
        private fun readResolve(): Any = FailedToInitialize
    }

    /**
     * Indicates a failure to retrieve the chat.
     */
    data object FailedToGetChat : ChatProviderError() {
        private fun readResolve(): Any = FailedToGetChat
    }

    /**
     * Indicates a failure to retrieve the chat ID.
     */
    data object FailedToGetChatId : ChatProviderError() {
        private fun readResolve(): Any = FailedToGetChatId
    }

    /**
     * Indicates a failure to retrieve the user identity.
     */
    data object FailedToGetIdentity : ChatProviderError() {
        private fun readResolve(): Any = FailedToGetIdentity
    }

    /**
     * Indicates a failure to join the chat.
     */
    data object FailedToJoin : ChatProviderError() {
        private fun readResolve(): Any = FailedToJoin
    }

    /**
     * Indicates a failure to retrieve chat messages.
     */
    data object FailedToGetChatMessages : ChatProviderError() {
        private fun readResolve(): Any = FailedToGetChatMessages
    }

    /**
     * Indicates that the conversation is invalid.
     */
    data object InvalidConversation : ChatProviderError() {
        private fun readResolve(): Any = InvalidConversation
    }

    /**
     * Indicates a failure to send a message.
     */
    data object FailedToSendMessage : ChatProviderError() {
        private fun readResolve(): Any = FailedToSendMessage
    }

    /**
     * Indicates a failure to leave the conversation.
     */
    data object FailedToLeaveConversation : ChatProviderError() {
        private fun readResolve(): Any = FailedToLeaveConversation
    }
}
