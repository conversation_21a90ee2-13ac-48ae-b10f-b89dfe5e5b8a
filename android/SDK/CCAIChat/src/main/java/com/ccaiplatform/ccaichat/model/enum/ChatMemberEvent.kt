package com.ccaiplatform.ccaichat.model.enum

/**
 * Represents member events in a chat, such as when a user joins or leaves.
 */
sealed class ChatMemberEvent {
    /**
     * Indicates that a user has joined the chat.
     *
     * @param identity The identifier of the user who joined, or null if unknown.
     */
    data class Joined(val identity: String?) : ChatMemberEvent()

    /**
     * Indicates that a user has left the chat.
     *
     * @param identity The identifier of the user who left, or null if unknown.
     */
    data class Left(val identity: String?) : ChatMemberEvent()
}
