package com.ccaiplatform.ccaichat.service

import com.ccaiplatform.ccaichat.model.SignedURLResponse
import com.ccaiplatform.ccaichat.model.enum.MediaUploadError
import com.ccaiplatform.ccaichat.model.enum.MimeType
import com.ccaiplatform.ccaikit.services.network.NetworkService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import org.mockito.exceptions.base.MockitoException
import org.mockito.kotlin.any

class ChatNetworkServiceMediaExtensionTest {

    @Mock
    private lateinit var networkService: NetworkService

    private lateinit var chatNetworkService: ChatNetworkService

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        chatNetworkService = ChatNetworkService(networkService, "deviceId", "en")
    }

    @Test
    fun uploadPhotosInternalEmptyPhotos() {
        runBlocking {
            val result = chatNetworkService.uploadPhotosInternal(1, emptyList(), "photo", null,null)
            assertTrue(result.isFailure)
            assertTrue(result.exceptionOrNull() is MediaUploadError.UploadError)
        }
    }

    @Test
    fun getUploadPhotoTaskMissingKey() {
        runBlocking {
            val chatId = 1
            val imageData = ByteArray(2) { 1 }
            val signedUrl = SignedURLResponse("http://upload", emptyMap())
            `when`(networkService.sendRequest<SignedURLResponse>(any(), any())).thenReturn(signedUrl)
            assertThrows(MediaUploadError.UploadError::class.java) {
                runBlocking {
                    chatNetworkService.getUploadPhotoTask(imageData, chatId, "photo", MimeType.JPEG.type)
                }
            }
        }
    }

    @Test
    fun requestPreSignedURLSuccess() {
        runBlocking {
            val chatId = 1
            val signedUrl = SignedURLResponse("http://upload", mapOf("key" to "photo1"))
            `when`(networkService.sendRequest<SignedURLResponse>(any(), any())).thenReturn(signedUrl)
            val result = chatNetworkService.requestPreSignedURL(chatId)
            assertTrue(result.isSuccess)
            assertEquals(signedUrl, result.getOrNull())
        }
    }

    @Test
    fun requestPreSignedURLFailure() {
        runBlocking {
            val chatId = 1
            `when`(networkService.sendRequest<SignedURLResponse>(any(), any())).thenThrow(MockitoException("error"))
            val result = chatNetworkService.requestPreSignedURL(chatId)
            assertTrue(result.isFailure)
            assertEquals("error", result.exceptionOrNull()?.message)
        }
    }
}
