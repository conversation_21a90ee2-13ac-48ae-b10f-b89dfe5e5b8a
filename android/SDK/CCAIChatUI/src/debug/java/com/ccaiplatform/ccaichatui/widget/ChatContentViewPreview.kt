package com.ccaiplatform.ccaichatui.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.tooling.preview.Preview
import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatStatus
import com.ccaiplatform.ccaichatui.models.Message

@Preview(showBackground = true, widthDp = 320)
@Composable
fun ChatContentViewPreview() {
    val messages = listOf(
        Message(
            text = "Hello",
            senderId = "end_user",
            type = ChatMessageBodyType.Text
        ),
        Message(
            text = "Hi",
            senderId = "agent",
            type = ChatMessageBodyType.Text
        )
    )

    val state by remember { mutableStateOf(ChatProviderState.Connected) }
    val escalateAbility by remember { mutableStateOf(true) }
    val accessory by remember { mutableStateOf(null) }
    val chatStatus by remember { mutableStateOf(ChatStatus.Assigned) }
    ChatContentView(
        messages = messages,
        chat = null,
        allAgents = mutableMapOf(),
        state = state,
        showIndicator = true,
        accessory = accessory,
        chatStatus = chatStatus,
        escalateAbility = escalateAbility,
        onBottomHeightCalculated = {},
        onUploadPhoto = { _, _, _-> },
        onTyping = {}
    )
}
