package com.ccaiplatform.ccaichatui.widget

import android.net.Uri
import androidx.compose.foundation.layout.Box
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import com.ccaiplatform.ccaichatui.R
import com.ccaiplatform.ccaichatui.util.MediaUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun MediaPickerButton(
    onImageUpload: suspend (ByteArray, Uri, String) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showMenu by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    val pickPhoto = MediaUtil.pickPhoto { uri ->
        scope.launch {
            isLoading = true
            withContext(Dispatchers.IO) {
                val mimeType = context.contentResolver.getType(uri)
                val data = MediaUtil.uriToByteArray(context, uri)
                if (data != null && mimeType != null) {
                    onImageUpload(data, uri, mimeType)
                }
            }
            isLoading = false
        }
    }
    val takePhoto = MediaUtil.takePhoto { uri ->
        scope.launch {
            isLoading = true
            withContext(Dispatchers.IO) {
                val mimeType = context.contentResolver.getType(uri)
                val data = MediaUtil.uriToByteArray(context, uri)
                if (data != null && mimeType != null) {
                    onImageUpload(data, uri, mimeType)
                }
            }
            isLoading = false
        }
    }

    Box(modifier = modifier) {
        IconButton(
            onClick = { showMenu = true },
            enabled = !isLoading
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = context.getString(R.string.attach_media)
            )
            if (isLoading) {
                androidx.compose.material3.CircularProgressIndicator()
            }
        }

        DropdownMenu(
            expanded = showMenu,
            onDismissRequest = { showMenu = false }
        ) {
            DropdownMenuItem(
                modifier = Modifier.semantics {
                    this.contentDescription = "chat take photo button"
                },
                text = { Text(context.getString(R.string.chat_take_photo)) },
                onClick = {
                    showMenu = false
                    takePhoto.invoke()
                }
            )
            DropdownMenuItem(
                modifier = Modifier.semantics {
                    this.contentDescription = "chat select photo button"
                },
                text = { Text(context.getString(R.string.select_photo)) },
                onClick = {
                    showMenu = false
                    pickPhoto.invoke()
                }
            )
        }
    }
}
