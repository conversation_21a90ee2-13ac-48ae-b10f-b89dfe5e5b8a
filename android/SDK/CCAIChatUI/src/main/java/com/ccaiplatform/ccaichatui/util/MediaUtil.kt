package com.ccaiplatform.ccaichatui.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.FileProvider
import androidx.exifinterface.media.ExifInterface
import com.ccaiplatform.ccaichat.model.enum.MimeType
import com.ccaiplatform.ccaichat.util.TempFileUtil
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File

object MediaUtil {
    private const val JPEG_COMPRESSION_QUALITY = 80

    @Composable
    fun pickPhoto(callback: (uri: Uri) -> Unit): (() -> Unit) {
        val context = LocalContext.current

        /*
         * The photo picker is available on devices that meet the following criteria:
         *
         * Run Android 11 (API level 30) or higher
         * Receive changes to Modular System Components through Google System Updates
         */
        val isPhotoPickerAvailable = remember {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable(context)
        }
        val modernPhotoPickerLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.PickVisualMedia()
        ) { uri ->
            uri?.let { callback.invoke(uri) }
        }
        val legacyPhotoPickerLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { uri ->
                    callback.invoke(uri)
                }
            }
        }
        return {
            if (isPhotoPickerAvailable) {
                modernPhotoPickerLauncher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            } else {
                val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                legacyPhotoPickerLauncher.launch(intent)
            }
        }
    }

    @Composable
    fun takePhoto(callback: (uri: Uri) -> Unit): (() -> Unit) {
        val context = LocalContext.current
        var photoUri by remember { mutableStateOf<Uri?>(null) }
        val takePictureLauncher = rememberLauncherForActivityResult(
            ActivityResultContracts.TakePicture()
        ) { success ->
            val uri = photoUri ?: return@rememberLauncherForActivityResult
            if (success) {
                callback.invoke(uri)
            }
        }
        val createImageUri = {
            val photosFile = TempFileUtil.getTakPhotoFile(context) ?: throw IllegalStateException("Failed to create image URI")
            val uri = FileProvider.getUriForFile(context, context.packageName + ".fileprovider", photosFile)
            uri
        }
        return {
            photoUri = createImageUri()
            takePictureLauncher.launch(photoUri)
        }
    }

    @Composable
    fun createDocument(mimeType: String): ((sourceFile: File) -> Unit) {
        var latestFile by remember { mutableStateOf<File?>(null) }
        val context = LocalContext.current
        val createFileLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.CreateDocument(mimeType)
        ) { uri: Uri? ->
            uri?.let {
                context.contentResolver.openOutputStream(it)?.use { outputStream ->
                    latestFile?.inputStream().use { inputStream ->
                        inputStream?.copyTo(outputStream)
                    }
                }
            }
        }
        return { sourceFile ->
            latestFile = sourceFile
            createFileLauncher.launch(sourceFile.name)
        }
    }

    fun uriToByteArray(context: Context, uri: Uri): ByteArray? {
        val mimeType = context.contentResolver.getType(uri)
        return when (mimeType) {
            MimeType.JPEG.type,
            MimeType.HEIC.type -> uriToFixedJpegByteArray(context, uri)
            else -> {
                return context.contentResolver.openInputStream(uri)?.use { inputStream -> inputStream.readBytes() }
            }
        }
    }

    private fun uriToFixedJpegByteArray(context: Context, uri: Uri): ByteArray? {
        // Step 1: Read image data once
        val imageBytes = context.contentResolver.openInputStream(uri)?.use {
            it.readBytes()
        } ?: return null

        // Step 2: Decode bitmap from byte array
        val bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size) ?: return null

        // Step 3: Read EXIF from byte array
        val exif = ExifInterface(ByteArrayInputStream(imageBytes))

        // Step 4: Get rotation from EXIF
        val rotation = when (exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)) {
            ExifInterface.ORIENTATION_ROTATE_90 -> 90
            ExifInterface.ORIENTATION_ROTATE_180 -> 180
            ExifInterface.ORIENTATION_ROTATE_270 -> 270
            else -> 0
        }

        // Step 5: Rotate if needed
        val rotatedBitmap = if (rotation == 0) bitmap else {
            val matrix = Matrix().apply { postRotate(rotation.toFloat()) }
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        }

        // Step 6: Compress to ByteArray
        val outputStream = ByteArrayOutputStream()
        rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, JPEG_COMPRESSION_QUALITY, outputStream)
        return outputStream.toByteArray()
    }
}
