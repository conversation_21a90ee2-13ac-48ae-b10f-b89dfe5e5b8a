package com.ccaiplatform.ccaichatui.widget

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.widget.Toast
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.ccaiplatform.ccaichat.model.ButtonAction
import com.ccaiplatform.ccaichat.model.ButtonMessage
import com.ccaiplatform.ccaichat.model.ChatRequestParameter
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.CustomFormMessage
import com.ccaiplatform.ccaichat.model.EscalationButton
import com.ccaiplatform.ccaichat.model.FormCompleteEvent
import com.ccaiplatform.ccaichat.model.FormMessage
import com.ccaiplatform.ccaichat.model.WebFormMessage
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichatui.ChatUIConstants.TYPING_POLL_INTERVAL_MS
import com.ccaiplatform.ccaichatui.ChatUIConstants.TYPING_TIMEOUT_SECONDS
import com.ccaiplatform.ccaichatui.ChatViewModel
import com.ccaiplatform.ccaichatui.R
import com.ccaiplatform.ccaichatui.models.AccessoryButton
import com.ccaiplatform.ccaichatui.models.AccessoryButtonAction
import com.ccaiplatform.ccaichatui.models.MessageHandleStatus
import com.ccaiplatform.ccaichatui.util.MediaUtil
import com.ccaiplatform.ccaichatui.widget.customform.CustomFormBottomSheetView
import com.ccaiplatform.ccaichatui.util.SystemUtil
import com.ccaiplatform.ccaichatui.widget.dialog.CustomDialog
import com.ccaiplatform.ccaichatui.widget.messageitem.ContentCardType
import com.ccaiplatform.ccaichatui.widget.webform.WebFormBottomSheetView
import com.ccaiplatform.ccaikit.models.logger.LogLevel
import com.ccaiplatform.ccaikit.models.response.queuemenu.QueueMenu
import com.ccaiplatform.ccaikit.util.ApplicationUtil
import com.ccaiplatform.ccaikit.util.PermissionUtil
import com.ccaiplatform.ccaikit.util.logging.LoggingUtil
import com.ccaiplatform.ccaiui.dialog.CircularProgressDialog
import com.ccaiplatform.ccaiui.models.CCAIUIState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatView(
    chatResponse: ChatResponse? = null,// this is used to pass the chat response from the previous screen
    menu: QueueMenu? = null,
    onNavigateBack: () -> Unit = {},
    onExitChat: () -> Unit = {}
) {
    val context = LocalContext.current
    val chatViewModel = viewModel<ChatViewModel>()

    var showNotificationPermissionDialog by remember { mutableStateOf(false) }
    var showNotificationOffDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        if (!PermissionUtil.isPermissionsForNotificationsGranted(context) &&
            !PermissionUtil.isSetNotificationPermissionOff(context)
        ) {
            showNotificationPermissionDialog = true
        }
    }

    DisposableEffect(Unit) {
        CCAIUIState.enterChatView()

        onDispose {
            CCAIUIState.exitChatView()
        }
    }

    if (chatResponse == null) {
        menu?.let {
            LaunchedEffect(menu.id) {
                chatViewModel.setMenu(menu.id)
            }
        }
    } else {
        chatViewModel.resumeChat(chatResponse)
        chatViewModel.menuId = chatResponse.menus?.firstOrNull()?.id ?: -1
        chatViewModel.getEscalateState()
    }

    val msgError = chatViewModel.errorMessage.collectAsState()
    LaunchedEffect(msgError.value) {
        msgError.value?.let { errorMessage ->
            Toast.makeText(context, errorMessage, Toast.LENGTH_LONG).show()
        }
    }

    val messages by chatViewModel.messages.collectAsState()
    val escalateAbility by chatViewModel.canEscalate.collectAsState()
    val state by chatViewModel.state.collectAsState()
    val remoteInputState by chatViewModel.isRemoteTyping.collectAsState()
    val showEndingDialog by chatViewModel.showEndingDialog.collectAsState()
    val chat by chatViewModel.chat.collectAsState()
    val accessory by chatViewModel.accessory.collectAsState()
    val timeout by chatViewModel.timeout.collectAsState()
    val coldTransfer by chatViewModel.coldTransfer.collectAsState()
    val chatStatus by chatViewModel.chatStatus.collectAsState()
    val isSending by chatViewModel.isSending.collectAsState()
    val afterHourMessage by chatViewModel.afterHourMessage.collectAsState()

    var showEscalationDialog by remember { mutableStateOf(false) }
    var showEndingChatDialog by remember { mutableStateOf(false) }
    var showStartNewFormDialog by remember { mutableStateOf(false) }
    var showCloseFormDialog by remember { mutableStateOf(false) }
    var isFormMinimized by remember { mutableStateOf(false) }
    var showFormErrorDialog by remember { mutableStateOf(false) }
    var showChatMenuBottomSheet by remember { mutableStateOf(false) }
    var isDownloadingTranscript by remember { mutableStateOf(false) }

    var currentForm by remember { mutableStateOf<FormMessage?>(null) }
    var bottomInputHeight by remember { mutableStateOf(0.dp) }
    val density = LocalDensity.current
    val screenHeight = with(density) { LocalConfiguration.current.screenHeightDp.dp }
    val statusBarHeight = WindowInsets.systemBars.asPaddingValues().calculateTopPadding()
    val topBarHeight = 56.dp  // Material3 TopAppBar  default height
    var counter by remember { mutableIntStateOf(0) }
    var isCounting by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()
    val createPdfDocument = MediaUtil.createDocument("application/pdf")

    LaunchedEffect(isCounting) {
        if (isCounting) {
            while (counter < TYPING_TIMEOUT_SECONDS) {
                delay(TYPING_POLL_INTERVAL_MS.toLong())
                counter++
                if (counter >= TYPING_TIMEOUT_SECONDS) {
                    isCounting = false
                    chatViewModel.typing(false)
                    counter = 0
                }
            }
        }
    }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = chat?.currentAgent?.name?.let { agentName ->
                            stringResource(R.string.chat_title_connected, agentName)
                        } ?: stringResource(R.string.chat),
                        fontSize = 18.sp
                    )
                },
                navigationIcon = {
                    IconButton(onClick = {
                        onNavigateBack()
                    }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    IconButton(
                        modifier = Modifier
                            .semantics {
                                this.contentDescription = "chat menu button"
                            },
                        onClick = { showChatMenuBottomSheet = true }
                    ) {
                        Icon(
                            imageVector = Icons.Filled.MoreVert,
                            contentDescription = stringResource(R.string.menu)
                        )
                    }
                }
            )
        },
        contentWindowInsets = WindowInsets.systemBars
    ) { innerPadding ->

        ChatContentView(
            messages = messages,
            chat = chat,
            allAgents = chatViewModel.getAllAgents(),
            state = state,
            showIndicator = remoteInputState,
            isSending = isSending,
            accessory = accessory,
            timeout = timeout,
            showWebView = currentForm != null,
            coldTransfer = coldTransfer,
            chatStatus = chatStatus,
            escalateAbility = escalateAbility,
            afterHourMessage = afterHourMessage,
            onTyping = { // handle local typing
                chatViewModel.startLocalTyping()
            },
            onInputTextChanged = { inputText ->
                if (inputText.isNotBlank()) {
                    chatViewModel.sendMessagePreview(inputText)
                }
            },
            loadHistoryMessages = {
                chatViewModel.loadHistoryMessages()
            },
            onUploadPhoto = { data, uri, mimeType ->
                chatViewModel.sendPhotoMessage(data, uri, mimeType)
            },
            innerPadding = innerPadding,
            onActionButtonClick = { button: Any ->
                handleActionButtonClick(
                    button, chatViewModel, onExitChat,
                    onFormClick = { form ->
                        if (form.smartActionId == currentForm?.smartActionId) {
                            isFormMinimized = false
                        } else {
                            if (currentForm == null) {
                                isFormMinimized = false
                                chatViewModel.handleFormStatus(form, MessageHandleStatus.InProgress)
                                currentForm = form
                            } else {
                                chatViewModel.presentForm = form
                                showStartNewFormDialog = true
                            }
                        }
                    },
                    onDownloadClick = { button ->
                        scope.launch {
                            chatViewModel.updateAccessoryButtonLoadingState(button, true)
                            val file = chatViewModel.downloadChatTranscript()
                            chatViewModel.updateAccessoryButtonLoadingState(button, false)
                            file ?: return@launch
                            createPdfDocument.invoke(file)
                        }
                    },
                    setShowEscalationDialog = {
                        showEscalationDialog = it
                    })
            },
            onBottomHeightCalculated = { height ->
                bottomInputHeight = height
            },
        )

        CircularProgressDialog(
            isShowing = state == ChatProviderState.Connecting,
            message = stringResource(R.string.connecting),
        )

        CircularProgressDialog(
            isShowing = showEndingDialog,
            message = stringResource(R.string.ending_chat),
        )

        if (showNotificationPermissionDialog) {
            val contentDescription = stringResource(R.string.notification_dialog_content_description)
            CustomDialog(
                modifier = Modifier.semantics {
                    this.contentDescription = contentDescription
                },
                title = context.getString(
                    R.string.dialog_notification_permission_title,
                    ApplicationUtil.getApplicationName(context)
                ),
                content = stringResource(R.string.dialog_notification_permission_content),
                dismissTitle = stringResource(R.string.btn_dont_allow),
                confirmTitle = stringResource(R.string.btn_allow),
                onDismiss = {
                    PermissionUtil.setNotificationPermissionOff(context)
                    showNotificationPermissionDialog = false
                    showNotificationOffDialog = true
                },
                onConfirm = {
                    showNotificationPermissionDialog = false
                    context.findActivity()?.let { activity ->
                        PermissionUtil.requestPermissionsForNotifications(activity)
                    } ?: run {
                        LoggingUtil.log("Context is not an Activity", LogLevel.ERROR)
                    }
                }
            )
        }

        if (showNotificationOffDialog) {
            val contentDescription = stringResource(R.string.notification_off_dialog_content_description)
            CustomDialog(
                modifier = Modifier.semantics {
                    this.contentDescription = contentDescription
                },
                title = stringResource(R.string.dialog_notification_off_title),
                content = stringResource(R.string.dialog_notification_off_content),
                dismissTitle = stringResource(R.string.btn_no_thanks),
                confirmTitle = stringResource(R.string.btn_go_settings),
                onDismiss = { showNotificationOffDialog = false },
                onConfirm = {
                    showNotificationOffDialog = false
                    SystemUtil.openNotificationSettings(context)
                }
            )
        }

        if (showEscalationDialog) {
            val contentDescription = stringResource(R.string.escalation_dialog_content_description)
            CustomDialog(
                modifier = Modifier.semantics {
                    this.contentDescription = contentDescription
                },
                title = null,
                content = stringResource(R.string.ask_confirm_txt),
                onDismiss = { showEscalationDialog = false },
                onConfirm = {
                    showEscalationDialog = false
                    chatViewModel.escalateToHuman()
                }
            )
        }

        if (showEndingChatDialog) {
            val contentDescription = stringResource(R.string.ending_chat_dialog_content_description)
            CustomDialog(
                modifier = Modifier.semantics {
                    this.contentDescription = contentDescription
                },
                content = stringResource(R.string.ask_end_chat_txt),
                onDismiss = { showEndingChatDialog = false },
                onConfirm = {
                    showEndingChatDialog = false
                    chatViewModel.endChat { onExitChat() }
                }
            )
        }

        if (showStartNewFormDialog) {
            CustomDialog(
                title = null,
                content = stringResource(R.string.dialog_start_new_form_content),
                confirmTitle = stringResource(R.string.start_new_form),
                dismissTitle = stringResource(R.string.cancel_txt),
                onDismiss = {
                    chatViewModel.presentForm = null
                    showStartNewFormDialog = false
                },
                onConfirm = {
                    currentForm = chatViewModel.presentForm
                    isFormMinimized = false
                    showStartNewFormDialog = false
                })
        }

        if (showCloseFormDialog) {
            CustomDialog(
                content = stringResource(R.string.dialog_close_form_content),
                confirmTitle = stringResource(R.string.close),
                dismissTitle = stringResource(R.string.cancel_txt),
                onDismiss = {
                    showCloseFormDialog = false
                },
                onConfirm = {
                    isFormMinimized = false
                    currentForm?.let {
                        chatViewModel.handleFormStatus(it, MessageHandleStatus.None)
                    }
                    currentForm = null
                    showCloseFormDialog = false
                })
        }

        if (showFormErrorDialog) {
            CustomDialog(
                content = stringResource(R.string.form_error),
                confirmTitle = stringResource(R.string.close),
                showDismissButton = false,
                onConfirm = {
                    showFormErrorDialog = false
                })
        }

        currentForm?.let { form ->
            FormView(
                form = form,
                isMinimized = isFormMinimized,
                onMinimize = { isFormMinimized = true },
                onExpand = { isFormMinimized = false },
                onDismiss = { showCloseFormDialog = true },
                onSubmit = {
                    currentForm = null
                    chatViewModel.handleFormStatus(form, MessageHandleStatus.Completed)
                },
                onFormComplete = { formCompleteData ->
                    when (formCompleteData.data?.status) {
                        "error" -> {
                            showFormErrorDialog = true
                            chatViewModel.handleFormCompleteEvent(formCompleteData, false)
                        }

                        "success" -> {
                            currentForm = null
                            chatViewModel.handleFormCompleteEvent(formCompleteData, true)
                        }

                        "cancelled" -> {
                            currentForm = null
                            chatViewModel.handleFormCompleteEvent(formCompleteData, false)
                        }

                        else -> {}
                    }
                }
            )
        }

        // Chat Menu Bottom Sheet
        if (showChatMenuBottomSheet) {
            ChatMenuBottomSheet(
                isDownloadTranscriptVisible = chatViewModel.doesMessageExchanged(),
                onDismiss = { showChatMenuBottomSheet = false },
                onDownloadTranscript = {
                    scope.launch {
                        isDownloadingTranscript = true
                        val file = chatViewModel.downloadChatTranscript()
                        isDownloadingTranscript = false
                        file?.let { createPdfDocument.invoke(it) }
                        showChatMenuBottomSheet = false
                    }
                },
                onExitChat = {
                    showChatMenuBottomSheet = false
                    showEndingChatDialog = true
                },
                isDownloading = isDownloadingTranscript
            )
        }
    }
}

@Composable
private fun FormView(
    form: FormMessage,
    isMinimized: Boolean,
    onMinimize: () -> Unit,
    onExpand: () -> Unit,
    onDismiss: () -> Unit,
    onSubmit: () -> Unit,
    onFormComplete: (FormCompleteEvent) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 0.dp)
    ) {
        when (form) {
            is WebFormMessage -> {
                WebFormBottomSheetView(
                    form = form,
                    isMinimized = isMinimized,
                    onMinimize = onMinimize,
                    onExpand = onExpand,
                    onDismiss = onDismiss,
                    onFormComplete = onFormComplete,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }

            is CustomFormMessage -> {
                CustomFormBottomSheetView(
                    form = form,
                    isMinimized = isMinimized,
                    onMinimize = onMinimize,
                    onExpand = onExpand,
                    onDismiss = onDismiss,
                    onSubmit = onSubmit,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }
        }
    }
}

private fun handleActionButtonClick(
    button: Any,
    chatViewModel: ChatViewModel,
    onExitChat: () -> Unit,
    onFormClick: (form: FormMessage) -> Unit,
    onDownloadClick: (button: AccessoryButton) -> Unit,
    setShowEscalationDialog: (Boolean) -> Unit
) {

    when (button) {
        is ButtonMessage -> {
            when (button.action) {
                ButtonAction.Escalation -> chatViewModel.escalateToHuman()
                ButtonAction.ContinueVirtualAgent -> {
                    val escalationId = button.escalationId
                    val actionValue = button.action?.value
                    if (escalationId != null && actionValue != null) {
                        chatViewModel.escalate(escalationId, actionValue)
                    }
                }

                ButtonAction.EndChat -> {
                    chatViewModel.endChat { onExitChat() }
                }

                else -> button.title?.let { chatViewModel.sendTextMessage(it) }
            }
        }

        is EscalationButton -> setShowEscalationDialog(true)

        is ContentCardType.Card -> {
            val contentCard = button.contentCard
            contentCard.title?.let {
                chatViewModel.sendTextMessage(it)
                chatViewModel.postEvent(name = ChatRequestParameter.CONTENT_CARD_CLICKED, title = it)
            }
        }

        is ContentCardType.Button -> {
            val contentCardButton = button.contentCardButton
            contentCardButton.cardTitle?.let {
                chatViewModel.sendTextMessage(it)
                chatViewModel.postEvent(
                    name = ChatRequestParameter.CONTENT_CARD_BUTTON_CLICKED,
                    title = it,
                    buttonTitle = contentCardButton.title
                )
            }
        }

        is FormMessage -> {
            chatViewModel.postEvent(name = ChatRequestParameter.FORM_CLICKED, title = button.name ?: "")
            onFormClick(button)
        }

        is AccessoryButton -> {
            when (button.action) {
                AccessoryButtonAction.NewChat -> {
                    chatViewModel.endChat { onExitChat() }
                }

                AccessoryButtonAction.Enqueue -> {
                    chatViewModel.enqueueCurrentChat()
                }

                AccessoryButtonAction.Restart -> {
                    onExitChat()
                }

                AccessoryButtonAction.Download -> {
                    onDownloadClick(button)
                }
            }
        }

        else -> LoggingUtil.log("Clicked on message: $button", LogLevel.INFO)
    }
}

fun Context.findActivity(): Activity? {
    return when (this) {
        is Activity -> this
        is ContextWrapper -> this.baseContext.findActivity()
        else -> null
    }
}
