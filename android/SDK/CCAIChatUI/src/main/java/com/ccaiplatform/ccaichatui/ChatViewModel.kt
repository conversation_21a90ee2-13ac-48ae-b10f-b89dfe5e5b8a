package com.ccaiplatform.ccaichatui

import android.app.Application
import android.net.Uri
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.ccaiplatform.ccaichat.chatService
import com.ccaiplatform.ccaichat.model.Asset
import com.ccaiplatform.ccaichat.model.ChatEvent
import com.ccaiplatform.ccaichat.model.ChatMessage
import com.ccaiplatform.ccaichat.model.ChatMessageBody
import com.ccaiplatform.ccaichat.model.ChatRequestParameter
import com.ccaiplatform.ccaichat.model.ChatResponse
import com.ccaiplatform.ccaichat.model.FormCompleteEvent
import com.ccaiplatform.ccaichat.model.FormMessage
import com.ccaiplatform.ccaichat.model.enum.ChatMemberEvent
import com.ccaiplatform.ccaichat.model.enum.ChatMessageBodyType
import com.ccaiplatform.ccaichat.model.enum.ChatProviderState
import com.ccaiplatform.ccaichat.model.enum.ChatStatus
import com.ccaiplatform.ccaichat.model.enum.ChatTransferEvent
import com.ccaiplatform.ccaichat.model.enum.ChatTypingEvent
import com.ccaiplatform.ccaichat.model.enum.OutgoingMessageContent
import com.ccaiplatform.ccaichat.service.ChatServiceInterface
import com.ccaiplatform.ccaichat.util.TempFileUtil
import com.ccaiplatform.ccaichatui.ChatUIConstants.TYPING_POLL_INTERVAL_MS
import com.ccaiplatform.ccaichatui.ChatUIConstants.TYPING_TIMEOUT_SECONDS
import com.ccaiplatform.ccaichatui.models.Accessory
import com.ccaiplatform.ccaichatui.models.AccessoryButton
import com.ccaiplatform.ccaichatui.models.AccessoryButtonAction
import com.ccaiplatform.ccaichatui.models.AccessoryStyle
import com.ccaiplatform.ccaichatui.models.Message
import com.ccaiplatform.ccaichatui.models.MessageHandleStatus
import com.ccaiplatform.ccaichatui.models.toSubmittedMessages
import com.ccaiplatform.ccaikit.CCAI
import com.ccaiplatform.ccaikit.interfaces.ChannelType
import com.ccaiplatform.ccaikit.interfaces.PushNotificationServiceInterface
import com.ccaiplatform.ccaikit.models.logger.LogLevel
import com.ccaiplatform.ccaikit.models.response.HttpExceptionWithCode
import com.ccaiplatform.ccaikit.models.response.communication.Agent
import com.ccaiplatform.ccaikit.models.response.communication.AgentType
import com.ccaiplatform.ccaikit.util.logging.LoggingUtil
import com.ccaiplatform.ccaikit.models.response.company.Company
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.net.HttpURLConnection
import com.ccaiplatform.ccaichatui.ChatUIConstants.MESSAGE_PREVIEW_DELAY
import java.util.Date
import kotlin.collections.plus

class ChatViewModel(application: Application) : AndroidViewModel(application) {
    var menuId: Int = -1

    private var needFilterMessages: Boolean = false

    private val service: ChatServiceInterface? = CCAI.chatService

    private val pushNotificationService: PushNotificationServiceInterface? = CCAI.pushNotificationService

    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    private val _completedFormMessages = MutableStateFlow<List<Message>>(emptyList())
    val completedFormMessages: StateFlow<List<Message>> = _completedFormMessages.asStateFlow()

    private val _sendMessageState = MutableStateFlow(false)
    val sendMessageState: StateFlow<Boolean> = _sendMessageState.asStateFlow()

    private val _inputMessage = MutableStateFlow<String>("")
    val inputMessage: StateFlow<String> = _inputMessage.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _state = MutableStateFlow<ChatProviderState>(ChatProviderState.None)
    val state: StateFlow<ChatProviderState> = _state.asStateFlow()

    private val _isRemoteTyping = MutableStateFlow(false)
    val isRemoteTyping: StateFlow<Boolean> = _isRemoteTyping.asStateFlow()

    private val _isTransferring = MutableStateFlow(false)
    val isTransferring: StateFlow<Boolean> = _isTransferring.asStateFlow()

    private val _chatStatus = MutableStateFlow<ChatStatus?>(null)
    val chatStatus: StateFlow<ChatStatus?> = _chatStatus.asStateFlow()

    private val _canEscalate = MutableStateFlow<Boolean?>(false)
    val canEscalate: StateFlow<Boolean?> = _canEscalate.asStateFlow()

    private val _chat = MutableStateFlow<ChatResponse?>(null)
    val chat: StateFlow<ChatResponse?> = _chat.asStateFlow()

    private var _showEndingDialog = MutableStateFlow<Boolean>(false)
    val showEndingDialog: StateFlow<Boolean> = _showEndingDialog.asStateFlow()

    private val _isSending = MutableStateFlow<Boolean>(false)
    val isSending: StateFlow<Boolean> = _isSending.asStateFlow()

    private var _accessory = MutableStateFlow<Accessory?>(null)
    val accessory: StateFlow<Accessory?> = _accessory.asStateFlow()

    private var _statusText = MutableStateFlow<String>("")
    val statusText: StateFlow<String> = _statusText.asStateFlow()

    private var _timeout = MutableStateFlow<Boolean>(false)
    val timeout: StateFlow<Boolean> = _timeout.asStateFlow()

    private var _coldTransfer = MutableStateFlow<Boolean>(false)
    val coldTransfer: StateFlow<Boolean> = _coldTransfer.asStateFlow()

    private var _afterHourMessage = MutableStateFlow<String?>(null)
    val afterHourMessage: StateFlow<String?> = _afterHourMessage.asStateFlow()

    private var allowSkipVirtualAgent = false

    private val application = getApplication<Application>()
    private val allAgents = mutableMapOf<String, Agent>()
    private var currentHistoryPageNumber = 1
    private var transferFromAgent: String? = null
    private var localTypingJob: Job? = null
    private var company: Company? = null
    private var ongoingInput: String = ""
    private var latestHistoryMesageTimestamp: Date? = null

    var presentForm: FormMessage? = null

    private var previewJob: Job? = null

    init {
        setupStateSubscriptions()
        setupMessageSubscriptions()
        setupPushNotificationSubscriptions()
        getCompany()
    }

    fun startChat() {
        launchWithErrorHandling(
            onError = { throwable ->
                if (throwable is HttpExceptionWithCode && throwable.code == HttpURLConnection.HTTP_CONFLICT) {
                    _afterHourMessage.update { throwable.message }
                } else {
                    _errorMessage.update {
                        application.getString(
                            R.string.failed_to_start_chat,
                            throwable.localizedMessage
                        )
                    }
                }
            }) {
            service?.start(menuId)
        }
    }

    fun resumeChat(chatResponse: ChatResponse) {
        launchWithErrorHandling(
            onError = { throwable ->
                _errorMessage.update {
                    application.getString(
                        R.string.failed_to_start_chat,
                        throwable.localizedMessage
                    )
                }
            }) {
            service?.resume(chatResponse)
        }
    }

    fun enqueueCurrentChat() {
        launchWithErrorHandling(
            onError = { throwable ->
                _errorMessage.update {
                    application.getString(
                        R.string.failed_to_start_chat,
                        throwable.localizedMessage
                    )
                }
            }) {
            service?.enqueueCurrentChat()
            _accessory.update { null }
        }
    }

    private fun sendMessage(message: OutgoingMessageContent) {
        _isSending.update { true }
        val messagesToSend = message.toSubmittedMessages()
        _messages.update { it + messagesToSend }
        needFilterMessages = true

        launchWithErrorHandling(onError = { throwable ->
            LoggingUtil.log("Error: ${throwable.localizedMessage}", LogLevel.ERROR)
            _isSending.update { false }
        }) {
            service?.sendMessage(message)
            _isSending.update { false }
        }
    }

    fun sendTextMessage(inputText: String) {
        val trimmedInputText = inputText.trim()
        if (trimmedInputText.isEmpty()) return

        val content = OutgoingMessageContent.Text(trimmedInputText)
        sendMessage(content)
    }

    fun sendPhotoMessage(data: ByteArray, uri: Uri, mimeType: String) {
        val content = OutgoingMessageContent.Photos(photos = listOf(data), uris = listOf(uri), contentType = mimeType)
        sendMessage(content)
    }

    fun typing(isTyping: Boolean) {
        service?.typing(isTyping)
    }

    fun sendMessagePreview(inputText: String) {
        if (company?.chatSettings?.messagePreview == true &&
            inputText != ongoingInput
        ) {
            sendMessagePreviewWithDebounce(inputText)
        }
    }

    fun endChat(onChatEndSuccess: () -> Unit) {
        launchWithErrorHandling(
            onError = { throwable ->
                _errorMessage.update {
                    application.getString(R.string.end_chat_fail_txt)
                }
                LoggingUtil.log("Failed to end chat: ${throwable.localizedMessage}", LogLevel.ERROR)
                _showEndingDialog.update { false }
            }) {
            _showEndingDialog.update { true }
            service?.endChat()?.let {
                if (it) {
                    onChatEndSuccess()
                } else {
                    _errorMessage.update { application.getString(R.string.end_chat_fail_txt) }
                }
            }
            _accessory.update { null }
            _showEndingDialog.update { false }
        }
    }

    fun escalateToHuman() {
        launchWithErrorHandling {
            service?.escalateToHumanAgent()
        }
    }

    fun escalate(escalationId: Int, deflectionChannel: String) {
        launchWithErrorHandling {
            service?.escalate(escalationId, deflectionChannel)
        }
    }

    fun postEvent(name: String, title: String, buttonTitle: String? = null) {
        val payload = buildMap {
            put(ChatRequestParameter.TITLE, title)
            buttonTitle?.let { put(ChatRequestParameter.BUTTON_TITLE, it) }
        }
        val event = ChatEvent(name, payload)
        launchWithErrorHandling {
            service?.sendEvent(event)
        }
    }

    private fun sendMessagePreviewWithDebounce(inputText: String) {
        if (state.value == ChatProviderState.Connected &&
            previewJob?.isActive != true
        ) {
            ongoingInput = inputText
            previewJob = viewModelScope.launch {
                delay(MESSAGE_PREVIEW_DELAY)
                service?.sendMessagePreview(ongoingInput)
            }
        }
    }

    private fun cancelMessagePreview() {
        previewJob?.cancel()
        previewJob = null
        ongoingInput = ""
    }

    private fun setupMessageSubscriptions() {
        viewModelScope.launch {
            val messagesReceivedJob = launch {
                service?.messagesReceivedSubject
                    ?.collect { chatMessages ->
                        val messagesValue = chatMessages
                            .map { it: ChatMessage ->
                                LoggingUtil.log("ChatMessage is $it", LogLevel.VERBOSE)
                                it.apply {
                                    val chatId = chat.value?.id ?: 0
                                    val mediaId = it.body.mediaId ?: 0
                                    if (it.body.type == ChatMessageBodyType.Photo && chatId > 0 && mediaId > 0) {
                                        it.body.document = Asset(TempFileUtil.getUploadedFilePathForPhoto(application, chatId, mediaId))
                                    }
                                }
                                Message(it)
                            }
                            .let { messages ->
                                if (needFilterMessages) {
                                    messages.filterNot { it.isSender }
                                } else {
                                    messages
                                }
                            }
                        _messages.update { it + messagesValue }
                        chatMessages.lastOrNull()?.body?.let { body ->
                            if (body.type == ChatMessageBodyType.Notification) {
                                if (body.timeout == true) {
                                    _timeout.update { true }
                                }
                            }
                        }
                        updateAccessoryButtons()
                    }
            }
            val messageSendJob = launch {
                service?.messageSendResultSubject
                    ?.collect { result ->
                        _sendMessageState.update { result }
                    }
            }

            messagesReceivedJob.join()
            messageSendJob.join()
        }
    }

    private fun chatProviderStateChanged(newState: ChatProviderState) {
        _state.update { newState }
        if (newState != ChatProviderState.Connected) {
            cancelMessagePreview()
        }
        updateAccessoryButtons()
        launchWithErrorHandling {
            if (newState == ChatProviderState.Connecting) {
                loadHistoryMessages()
            }
        }
        launchWithErrorHandling {
            service?.checkStatus()
        }
    }

    private fun didReceiveMemberEvent(event: ChatMemberEvent) {
        when (event) {
            is ChatMemberEvent.Joined -> {
                LoggingUtil.log(
                    "${event.identity ?: ""} joined",
                    LogLevel.DEBUG
                )
            }

            is ChatMemberEvent.Left -> {
                LoggingUtil.log(
                    "${event.identity ?: ""} left",
                    LogLevel.DEBUG
                )
                if (isTransferring.value && event.identity == transferFromAgent) {
                    _coldTransfer.update { true }
                }
            }
        }
        launchWithErrorHandling {
            service?.checkStatus()
        }
    }

    private fun didReceiveTransferEvent(event: ChatTransferEvent) {
        when (event) {
            is ChatTransferEvent.Started -> {
                LoggingUtil.log(
                    "${event.identity ?: ""} transfer start",
                    LogLevel.DEBUG
                )
                _isTransferring.update { true }
                _coldTransfer.update { false }
                transferFromAgent = event.identity
            }

            is ChatTransferEvent.Accepted -> {
                LoggingUtil.log(
                    "${event.identity ?: ""} transfer success",
                    LogLevel.DEBUG
                )
                _isTransferring.update { false }
                _coldTransfer.update { false }
                transferFromAgent = null
            }

            is ChatTransferEvent.Failed -> {
                LoggingUtil.log(
                    "${event.identity ?: ""} transfer failed",
                    LogLevel.DEBUG
                )
                _isTransferring.update { false }
                _coldTransfer.update { false }
                transferFromAgent = null
            }
        }
    }

    private fun setupStateSubscriptions() {
        viewModelScope.launch {
            val stateChangeJob = launch {
                service?.stateChangedSubject
                    ?.distinctUntilChanged()
                    ?.collect { newState ->
                        chatProviderStateChanged(newState)
                    }
            }

            val typingEventJob = launch {
                service?.typingEventSubject
                    ?.collect { event ->
                        _isRemoteTyping.update {
                            when (event) {
                                is ChatTypingEvent.Started -> true
                                is ChatTypingEvent.Ended -> false
                            }
                        }
                    }
            }

            val memberEventJob = launch {
                service?.memberEventSubject
                    ?.collect { event ->
                        didReceiveMemberEvent(event)
                    }
            }

            val transferEventJob = launch {
                service?.transferEventSubject
                    ?.collect { event ->
                        didReceiveTransferEvent(event)
                    }
            }
            val chatReceivedJob = launch {
                service?.chatReceivedSubject
                    ?.collect { chat ->
                        LoggingUtil.log("Received chat: ${chat.id}")
                        _chat.update { chat }
                        updateEscalateState()
                        handleChatUpdate(chat)
                        updateAgents(chat)
                    }
            }
            val checkStatusJob = launch {
                service?.checkStatusSubject
                    ?.collect { map: Map<String, Any> ->
                        if (map["check"] == true) {
                            stopMonitoring()
                            startMonitoring(map["chat"] as ChatResponse, this)
                        } else {
                            stopMonitoring()
                        }
                    }
            }

            stateChangeJob.join()
            typingEventJob.join()
            memberEventJob.join()
            transferEventJob.join()
            chatReceivedJob.join()
            checkStatusJob.join()
        }
    }

    private fun setupPushNotificationSubscriptions() {
        viewModelScope.launch {
            val requestNotificationReceivedJob = launch {
                pushNotificationService?.requestNotificationReceivedSubject
                    ?.collect { _ ->
                        LoggingUtil.log(
                            "Received request notification",
                            LogLevel.DEBUG
                        )
                    }
            }
            val chatNotificationReceivedJob = launch {
                pushNotificationService?.chatNotificationReceivedSubject
                    ?.collect { _ ->
                        LoggingUtil.log(
                            "Received chat notification",
                            LogLevel.DEBUG
                        )

                        // We need to request latest chat status when we receive "chat_" type push messages
                        launchWithErrorHandling {
                            service?.checkStatus()
                        }
                    }
            }
            val transferNotificationReceivedJob = launch {
                pushNotificationService?.transferNotificationReceivedSubject
                    ?.collect { _ ->
                        LoggingUtil.log(
                            "Received transfer notification",
                            LogLevel.DEBUG
                        )
                    }
            }
            val customNotificationReceivedJob = launch {
                pushNotificationService?.customNotificationReceivedSubject
                    ?.collect { _ ->
                        LoggingUtil.log(
                            "Received custom notification",
                            LogLevel.DEBUG
                        )
                    }
            }

            requestNotificationReceivedJob.join()
            chatNotificationReceivedJob.join()
            transferNotificationReceivedJob.join()
            customNotificationReceivedJob.join()
        }
    }

    private fun getCompany() {
        viewModelScope.launch {
            company = CCAI.companyService?.getCompany()
        }
    }

    fun getEscalateState() {
        launchWithErrorHandling {
            allowSkipVirtualAgent = CCAI.queueMenuService?.getVirtualAgentSettings(
                menuId,
                ChannelType.CHAT
            )?.allowSkipVirtualAgent == true
            updateEscalateState()
        }
    }

    fun getAllAgents(): Map<String, Agent> {
        return allAgents
    }

    private fun updateAgents(chat: ChatResponse) {
        val agents = (chat.allAgents.orEmpty()) + (chat.allVirtualAgents.orEmpty())

        val newAgents = agents
            .filter { it.agentIdString != null && allAgents[it.agentIdString] == null }
            .associateBy { it.agentIdString.orEmpty() }
        if (newAgents.isNotEmpty()) {
            allAgents.putAll(newAgents)
        }
    }

    suspend fun loadHistoryMessages(): Int {
        runCatching {
            if (currentHistoryPageNumber > 0) {
                service?.getPreviousChats(
                    currentHistoryPageNumber,
                    if (currentHistoryPageNumber == 1) 2 else 1
                )
            } else {
                null
            }
        }.onSuccess { chatHistoryInfo ->
            val chatMessages = chatHistoryInfo?.chatMessages.orEmpty()
            val agents = chatHistoryInfo?.agents.orEmpty()
            val nextPage = chatHistoryInfo?.nextPage ?: -1
            currentHistoryPageNumber = nextPage

            val agentMap = agents.mapNotNull { agent ->
                agent.agentIdString?.let { id -> id to agent }
            }.toMap()

            allAgents.putAll(agentMap)
            chatMessages.forEach {
                it.apply {
                    val chatId = chat.value?.id ?: 0
                    val mediaId = it.body.mediaId ?: 0
                    if (it.body.type == ChatMessageBodyType.Photo && chatId > 0 && mediaId > 0) {
                        it.body.document = Asset(TempFileUtil.getUploadedFilePathForPhoto(application, chatId, mediaId))
                    }
                }
            }
            val messages = chatMessages.map(::Message).sortedBy { it.timestamp }
            if (messages.isNotEmpty()) {
                val lastTimestamp = messages.lastOrNull()?.timestamp
                if (lastTimestamp != null && (latestHistoryMesageTimestamp == null || latestHistoryMesageTimestamp?.before(lastTimestamp) == true)) {
                    latestHistoryMesageTimestamp = lastTimestamp
                }
                val allMessages = messages + this.messages.value
                _messages.update { allMessages }
            }

            LoggingUtil.log("Loaded ${messages.size} history messages", LogLevel.DEBUG)
            return messages.size
        }.onFailure { e ->
            LoggingUtil.log(
                message = "Failed to load history messages: ${e.localizedMessage}",
                level = LogLevel.ERROR,
                throwable = e
            )
        }
        return 0
    }

    private fun updateEscalateState() {
        val isVirtualAgent = _chat.value?.currentAgent?.type == AgentType.Virtual
        _canEscalate.update { allowSkipVirtualAgent && isVirtualAgent }
    }

    private fun handleChatUpdate(chat: ChatResponse?) {
        val statusHasChanged = chat?.status != chatStatus.value
        LoggingUtil.log("chat_status:${chat?.status}", LogLevel.INFO)
        _chatStatus.update { chat?.status }
        if (statusText.value != chat?.statusText) {
            _statusText.update { chat?.statusText ?: "" }
        }
        val timeout = chat?.timeoutAt?.isEmpty() == false
        if (this.timeout.value != timeout) {
            this._timeout.update { timeout }
        }
        if (statusHasChanged) {
            updateAccessoryButtons()
        }
    }

    private var monitoringJob: Job? = null
    private val defaultInterval = 5_000L
    private val longInterval = 60_000L

    private fun startMonitoring(chatResponse: ChatResponse, scope: CoroutineScope) {
        if (monitoringJob?.isActive == true) return
        val interval =
            if (chatResponse.status?.isInProgress() == true) longInterval else defaultInterval
        monitoringJob = scope.launch {
            while (isActive) {
                try {
                    service?.checkStatus()
                } catch (e: Exception) {
                    LoggingUtil.log(
                        "checkStatus failed: ${e.localizedMessage}",
                        LogLevel.ERROR,
                        throwable = e
                    )
                    break
                }
                delay(interval)
            }
            handleMonitoringStop()
        }
    }

    private fun stopMonitoring() {
        monitoringJob?.cancel()
        monitoringJob = null
    }

    private fun stopLocalTyping() {
        localTypingJob?.cancel()
        localTypingJob = null
    }

    private fun handleMonitoringStop() {
    }

    override fun onCleared() {
        stopMonitoring()
        stopLocalTyping()
        super.onCleared()
    }

    private fun launchWithErrorHandling(
        onError: (Throwable) -> Unit = { e ->
            LoggingUtil.log("Error: ${e.localizedMessage}", LogLevel.ERROR)
        },
        block: suspend () -> Unit
    ) {
        viewModelScope.launch {
            runCatching {
                block()
            }.onFailure {
                onError(it)
            }
        }
    }

    fun setMenu(menuId: Int) {
        this.menuId = menuId
        startChat()
        getEscalateState()
    }

    fun updateAccessoryButtonLoadingState(button: AccessoryButton, isLoading: Boolean) {
        _accessory.update { current ->
            current?.copy(
                buttons = current.buttons.map {
                    if (it.action == button.action) it.copy(isLoading = isLoading) else it
                }
            )
        }
    }

    private fun updateAccessoryButtons() {
        val accessoryValue = when (state.value) {
            is ChatProviderState.Connected -> {
                when {
                    chatStatus.value?.isDismissed() == true -> {
                        Accessory(
                            title = application.getString(R.string.accessory_dismissed_title),
                            buttons = listOf(
                                AccessoryButton(
                                    title = application.getString(R.string.same),
                                    action = AccessoryButtonAction.Enqueue
                                ),
                                AccessoryButton(
                                    title = application.getString(R.string.new_txt),
                                    action = AccessoryButtonAction.NewChat
                                )
                            ),
                            style = AccessoryStyle.Inline
                        )
                    }

                    timeout.value ||
                        chatStatus.value?.isCompleted() == true -> {
                        var buttons = mutableListOf(
                            AccessoryButton(
                                title = application.getString(R.string.start_a_new_chat),
                                action = AccessoryButtonAction.NewChat
                            ),
                            AccessoryButton(
                                title = application.getString(R.string.exit_chat),
                                action = AccessoryButtonAction.NewChat
                            )
                        )
                        if (doesMessageExchanged()) {
                            buttons.add(
                                0, AccessoryButton(
                                    title = application.getString(R.string.download_transcript),
                                    action = AccessoryButtonAction.Download,
                                )
                            )
                        }
                        Accessory(
                            buttons = buttons,
                            style = AccessoryStyle.Inline
                        )
                    }

                    else -> null
                }
            }

            is ChatProviderState.Error -> {
                Accessory(
                    buttons = listOf(
                        AccessoryButton(
                            title = application.getString(R.string.restart_chat),
                            action = AccessoryButtonAction.Restart
                        )
                    ),
                    style = AccessoryStyle.Inline
                )
            }

            else -> null
        }
        _accessory.update { accessoryValue }
    }

    fun handleFormCompleteEvent(formCompleteData: FormCompleteEvent, success: Boolean) {
        launchWithErrorHandling {
            formCompleteData.data?.let {
                val content = OutgoingMessageContent.FormComplete(
                    type = formCompleteData.type,
                    signature = formCompleteData.signature,
                    data = it
                )
                sendMessage(content)
            }

            val status = if (success) MessageHandleStatus.Completed else MessageHandleStatus.None
            formCompleteData.data?.smartActionId?.let { handleSmartActionStatus(it, status) }
        }
    }

    fun handleFormStatus(formMessage: FormMessage, status: MessageHandleStatus) {
        presentForm = formMessage
        formMessage.smartActionId?.let { handleSmartActionStatus(it, status) }
    }

    fun handleSmartActionStatus(smartActionId: Int, status: MessageHandleStatus) {
        _messages.update { messages ->
            messages.map { message ->
                if (message.form?.smartActionId == smartActionId) {
                    message.copy(handleStatus = status)
                } else {
                    message
                }
            }
        }
    }

    fun startLocalTyping() {
        typing(true)
        localTypingJob?.cancel()
        localTypingJob = viewModelScope.launch {
            val timeout = TYPING_TIMEOUT_SECONDS * 1000L
            val interval = TYPING_POLL_INTERVAL_MS.toLong()
            var elapsed = 0L
            while (elapsed < timeout) {
                delay(interval)
                elapsed += interval
            }
            typing(false)
        }
    }

    suspend fun downloadChatTranscript(): File? {
        return service?.downloadChatTranscript()
    }

    fun doesMessageExchanged(): Boolean {
        var incomingCount = 0
        var outgoingFound = false
        messages.value.forEach { message ->
            if (latestHistoryMesageTimestamp == null ||
                message.timestamp.after(latestHistoryMesageTimestamp)
            ) {
                if (message.isSender) {
                    outgoingFound = true
                } else {
                    incomingCount += 1
                }
            }
        }

        return incomingCount > 1 && outgoingFound
    }
}
