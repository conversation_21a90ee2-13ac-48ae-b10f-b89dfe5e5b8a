[versions]
agp = "8.9.1"
cobrowseSdkAndroid = "3.8.0"
constraintlayoutCompose = "1.1.1"
jjwt = "0.6.0"
junitJupiter = "5.10.2"
kotlin = "2.1.10"
coreKtx = "1.15.0"
espressoCore = "3.6.1"
appcompat = "1.7.0"
material = "1.12.0"
lifecycleRuntimeKtx = "2.8.7"
activityCompose = "1.10.1"
composeBom = "2025.03.01"
okhttpVersion = "4.12.0"
okhttpInterceptorVersion = "4.11.0"
gsonVersion = "2.11.0"
kotlinxSerialization = "1.7.3"
robolectric = "4.11.1"
twilioConversationsVersion = "6.1.1"
navigation-compose = "2.8.9"
mockitoKotlinVersion = "5.3.1"
mockitoVersion = "5.10.0"
mockitoInlineVersion = "5.2.0"
androidUnitVersion = "1.2.1"
junit4 = "4.13.2"
mockitoAndroidVersion = "5.17.0"
kotlinxCoroutines = "1.8.0"
coilCompose = "2.4.0"
firebaseBom = "33.14.0"
firebaseMessagingKtx = "24.1.1"
preferenceKtx = "1.2.1"
googleServices = "4.4.2"
firebase-crashlytics = "3.0.4"
material2 = "1.7.0"
work = "2.10.1"
lifecycleProcess = "2.6.1"
exifinterface = "1.3.6"

[libraries]
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-material = { module = "androidx.compose.material:material" }
androidx-test-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidUnitVersion" }
cobrowse-sdk-android = { module = "io.cobrowse:cobrowse-sdk-android", version.ref = "cobrowseSdkAndroid" }
junit-jupiter = { module = "org.junit.jupiter:junit-jupiter", version.ref = "junitJupiter" }
kotlin-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }
junit4 = { group = "junit", name = "junit", version.ref = "junit4" }
mockito-kotlin = { group = "org.mockito.kotlin", name = "mockito-kotlin", version.ref = "mockitoKotlinVersion" }
mockito-core = { group = "org.mockito", name = "mockito-core", version.ref = "mockitoVersion" }
mockito-inline = { group = "org.mockito", name = "mockito-inline", version.ref = "mockitoInlineVersion" }
mockito-android = { group = "org.mockito", name = "mockito-android", version.ref = "mockitoAndroidVersion" }
mockwebserver = { group = "com.squareup.okhttp3", name = "mockwebserver", version.ref = "okhttpVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
okhttp3 = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttpVersion" }
okhttp3-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttpInterceptorVersion" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gsonVersion" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
conversations-android = { group = "com.twilio", name = "conversations-android", version.ref = "twilioConversationsVersion" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation-compose" }
jjwt = { module = "io.jsonwebtoken:jjwt", version.ref = "jjwt" }
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
coil-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coilCompose" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-messaging-ktx = { group = "com.google.firebase", name = "firebase-messaging-ktx", version.ref = "firebaseMessagingKtx" }
firebase-crashlytics-ktx = { module = "com.google.firebase:firebase-crashlytics-ktx" }
firebase-analytics-ktx = { module = "com.google.firebase:firebase-analytics-ktx" }
androidx-preference-ktx = { group = "androidx.preference", name = "preference-ktx", version.ref = "preferenceKtx" }
androidx-work = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycleProcess" }
androidx-exifinterface = { group = "androidx.exifinterface", name = "exifinterface", version.ref = "exifinterface" }

[bundles]
androidx-compose = ["androidx-ui", "androidx-ui-graphics", "androidx-ui-test-manifest", "androidx-material3"]
androidx-compose-debug = ["androidx-ui-tooling", "androidx-ui-tooling-preview"]


[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices"}
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebase-crashlytics" }
